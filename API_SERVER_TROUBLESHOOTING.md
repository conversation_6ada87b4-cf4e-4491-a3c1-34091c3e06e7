# API Server Troubleshooting Guide

## Issue Description

When hitting `http://localhost:8000/api/v1/client-strategies`, the server returns an HTML webpage instead of a JSON API response.

## Root Cause

The Python server running on port 8000 (`venv/bin/python run.py`) appears to be configured to serve static files instead of API endpoints, even though the OpenAPI specification indicates it should be a FastAPI server with proper API routes.

## Current Status

- **Temporary Fix Applied**: The application is now configured to use mock data by default (`ApiConfig.useMockData = true`)
- **React Key Issue Fixed**: Added index to table row keys to prevent duplicate key warnings
- **Better Error Handling**: Added detection for HTML responses from API endpoints

## Symptoms

1. **API Endpoint Returns HTML**: 
   ```bash
   curl http://localhost:8000/api/v1/client-strategies
   # Returns HTML instead of JSON
   ```

2. **React Console Warnings**:
   ```
   Warning: Encountered two children with the same key
   ```

## Solutions

### Immediate Solution (Applied)
- Switch to mock data mode temporarily
- Fixed React key duplication issue
- Added better error detection

### Long-term Solutions

#### Option 1: Check Backend Server Configuration
1. Verify the Python server is running the correct application
2. Check if `run.py` is configured to serve API routes instead of static files
3. Ensure FastAPI routes are properly registered

#### Option 2: Start Correct API Server
If there's a separate backend project:
1. Navigate to the backend directory
2. Start the FastAPI server:
   ```bash
   uvicorn main:app --host 0.0.0.0 --port 8000 --reload
   ```

#### Option 3: Check Server Configuration
The server might be misconfigured to serve static files. Check:
- Server routing configuration
- Static file serving settings
- API route registration

## Testing the Fix

1. **Test with Mock Data** (Current Setup):
   - Navigate to Client Strategies page
   - Should see mock data without errors
   - No duplicate key warnings in console

2. **Test API Server** (When Fixed):
   - Use the API toggle to switch to live data
   - Should receive JSON responses from API endpoints
   - Check browser network tab for proper API responses

## Files Modified

1. `src/pages/ClientStrategies.tsx` - Fixed duplicate key issue
2. `src/api/config.ts` - Temporarily enabled mock data
3. `src/services/ClientStrategyService.ts` - Added HTML response detection
4. `src/components/ApiToggle.tsx` - Updated user messaging

## Next Steps

1. Investigate the backend server configuration
2. Ensure proper FastAPI application is running
3. Test API endpoints return JSON instead of HTML
4. Switch back to live API mode once server is fixed
