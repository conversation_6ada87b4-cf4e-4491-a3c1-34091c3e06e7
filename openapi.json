{"openapi": "3.1.0", "info": {"title": "FastAPI ClickHouse App", "version": "1.0.0"}, "paths": {"/api/v1/users/": {"post": {"tags": ["users"], "summary": "Create User", "description": "Create a new user.", "operationId": "create_user_api_v1_users__post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["users"], "summary": "List Users", "description": "List all users with pagination.", "operationId": "list_users_api_v1_users__get", "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "Offset"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserResponse"}, "title": "Response List Users Api V1 Users  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/{user_id}": {"get": {"tags": ["users"], "summary": "Get User", "description": "Get a user by ID.", "operationId": "get_user_api_v1_users__user_id__get", "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/UserResponse"}, {"type": "null"}], "title": "Response Get User Api V1 Users  User Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/": {"post": {"tags": ["clients"], "summary": "Create Client", "description": "Create a new client.\n\nThis endpoint creates a new client with the provided details.", "operationId": "create_client_api_v1_clients__post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["clients"], "summary": "List Clients", "description": "List all clients with optional filtering.\n\nReturns a paginated list of clients. Can be filtered by active status and broker.", "operationId": "list_clients_api_v1_clients__get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "is_active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}, {"name": "broker", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Broker"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/{client_id}": {"get": {"tags": ["clients"], "summary": "Get Client", "description": "Get a client by ID.\n\nReturns the client details for the given client ID.", "operationId": "get_client_api_v1_clients__client_id__get", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Client Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["clients"], "summary": "Update Client", "description": "Update a client.\n\nUpdates the client details for the given client ID.\nOnly the fields provided in the request will be updated.", "operationId": "update_client_api_v1_clients__client_id__put", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Client Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["clients"], "summary": "Delete Client", "description": "Delete a client.\n\nMarks the client as inactive in the database (soft delete).", "operationId": "delete_client_api_v1_clients__client_id__delete", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Client Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/clients/{client_id}/toggle": {"put": {"tags": ["clients"], "summary": "Toggle Client", "description": "Toggle the active status of a client.\n\nToggles the active status of the client with the given client ID.", "operationId": "toggle_client_api_v1_clients__client_id__toggle_put", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Client Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/strategies/": {"post": {"tags": ["strategies"], "summary": "Create a new strategy", "description": "Create a new strategy with the given details.\n\n- **name**: Strategy name (must be unique)\n- **description**: Optional strategy description\n- **parameters**: Strategy parameters in JSON string format\n- **is_active**: Whether the strategy is active (default: True)", "operationId": "create_strategy_api_v1_strategies__post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StrategyCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["strategies"], "summary": "List all strategies", "description": "List all strategies with pagination.\n\n- **skip**: Number of records to skip (for pagination)\n- **limit**: Maximum number of records to return (for pagination)", "operationId": "list_strategies_api_v1_strategies__get", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/strategies/{strategy_id}": {"get": {"tags": ["strategies"], "summary": "Get a strategy by ID", "description": "Get a strategy by its ID.\n\n- **strategy_id**: UUID of the strategy to retrieve", "operationId": "get_strategy_api_v1_strategies__strategy_id__get", "parameters": [{"name": "strategy_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Strategy Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["strategies"], "summary": "Update a strategy", "description": "Update a strategy's details.\n\n- **strategy_id**: UUID of the strategy to update\n- **name**: Optional new name for the strategy\n- **description**: Optional new description\n- **parameters**: Optional new parameters in JSON string format\n- **is_active**: Optional new active status", "operationId": "update_strategy_api_v1_strategies__strategy_id__put", "parameters": [{"name": "strategy_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Strategy Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StrategyUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["strategies"], "summary": "Delete a strategy", "description": "Delete a strategy by ID.\n\n- **strategy_id**: UUID of the strategy to delete", "operationId": "delete_strategy_api_v1_strategies__strategy_id__delete", "parameters": [{"name": "strategy_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Strategy Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/strategies/{strategy_id}/toggle-status": {"post": {"tags": ["strategies"], "summary": "Toggle strategy active status", "description": "Toggle the active status of a strategy.\n\n- **strategy_id**: UUID of the strategy to toggle", "operationId": "toggle_strategy_status_api_v1_strategies__strategy_id__toggle_status_post", "parameters": [{"name": "strategy_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Strategy Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StrategyResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/client-strategies/": {"post": {"tags": ["client-strategies"], "summary": "Create a new client-strategy mapping", "description": "Create a new mapping between a client and a strategy.\n\n- **client_id**: ID of the client\n- **strategy_id**: ID of the strategy\n- **variety**: Strategy variety (NORMAL or ROBO)\n- **is_enabled**: Whether the strategy is enabled for the client", "operationId": "create_client_strategy_api_v1_client_strategies__post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientStrategyCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["client-strategies"], "summary": "List client-strategy mappings", "description": "List all client-strategy mappings with optional filtering.\n\n- **client_id**: Optional filter by client ID\n- **strategy_id**: Optional filter by strategy ID\n- **skip**: Number of records to skip (pagination)\n- **limit**: Maximum number of records to return (pagination)", "operationId": "list_client_strategies_api_v1_client_strategies__get", "parameters": [{"name": "client_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter by client ID", "title": "Client Id"}, "description": "Filter by client ID"}, {"name": "strategy_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter by strategy ID", "title": "Strategy Id"}, "description": "Filter by strategy ID"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of records to skip", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Number of records to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Number of records to return", "default": 100, "title": "Limit"}, "description": "Number of records to return"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/client-strategies/{client_id}/{strategy_id}": {"get": {"tags": ["client-strategies"], "summary": "Get a client-strategy mapping", "description": "Get a specific client-strategy mapping.\n\n- **client_id**: UUID of the client\n- **strategy_id**: UUID of the strategy", "operationId": "get_client_strategy_api_v1_client_strategies__client_id___strategy_id__get", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Client ID", "title": "Client Id"}, "description": "Client ID"}, {"name": "strategy_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Strategy ID", "title": "Strategy Id"}, "description": "Strategy ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["client-strategies"], "summary": "Update a client-strategy mapping", "description": "Update a client-strategy mapping.\n\n- **client_id**: UUID of the client\n- **strategy_id**: UUID of the strategy\n- **variety**: Optional new variety (NORMAL or ROBO)\n- **is_enabled**: Optional new enabled status", "operationId": "update_client_strategy_api_v1_client_strategies__client_id___strategy_id__put", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Client ID", "title": "Client Id"}, "description": "Client ID"}, {"name": "strategy_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Strategy ID", "title": "Strategy Id"}, "description": "Strategy ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientStrategyUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["client-strategies"], "summary": "Delete a client-strategy mapping", "description": "Delete a client-strategy mapping.\n\n- **client_id**: UUID of the client\n- **strategy_id**: UUID of the strategy", "operationId": "delete_client_strategy_api_v1_client_strategies__client_id___strategy_id__delete", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Client ID", "title": "Client Id"}, "description": "Client ID"}, {"name": "strategy_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Strategy ID", "title": "Strategy Id"}, "description": "Strategy ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/client-strategies/{client_id}/{strategy_id}/toggle-status": {"post": {"tags": ["client-strategies"], "summary": "Toggle client-strategy status", "description": "Toggle the enabled status of a client-strategy mapping.\n\n- **client_id**: UUID of the client\n- **strategy_id**: UUID of the strategy", "operationId": "toggle_client_strategy_status_api_v1_client_strategies__client_id___strategy_id__toggle_status_post", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Client ID", "title": "Client Id"}, "description": "Client ID"}, {"name": "strategy_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Strategy ID", "title": "Strategy Id"}, "description": "Strategy ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/client-strategy-rules/{client_id}/{strategy_id}": {"post": {"tags": ["client-strategy-rules"], "summary": "Create a new client strategy rule", "description": "Create a new trading rule for a client strategy.\n\n- **client_id**: ID of the client (from URL path)\n- **strategy_id**: ID of the strategy (from URL path)\n- **price_min**: Minimum price for the rule to be active\n- **price_max**: Maximum price for the rule to be active\n- **target**: Target price or percentage\n- **stop_loss**: Stop loss price or percentage\n- **trailing_sl**: Optional trailing stop loss\n- **quantity**: Quantity to trade\n- **mode**: Calculation mode ('percent' or 'points')\n- **expiry_type**: Type of expiry/instrument (CE/PE/FUT/EQ/OPT)", "operationId": "create_rule_api_v1_client_strategy_rules__client_id___strategy_id__post", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "description": "ID of the client", "title": "Client Id"}, "description": "ID of the client"}, {"name": "strategy_id", "in": "path", "required": true, "schema": {"type": "string", "description": "ID of the strategy", "title": "Strategy Id"}, "description": "ID of the strategy"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientStrategyRuleCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["client-strategy-rules"], "summary": "List rules for a specific client and strategy", "description": "List all trading rules for a specific client and strategy.", "operationId": "list_rules_api_v1_client_strategy_rules__client_id___strategy_id__get", "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "description": "ID of the client", "title": "Client Id"}, "description": "ID of the client"}, {"name": "strategy_id", "in": "path", "required": true, "schema": {"type": "string", "description": "ID of the strategy", "title": "Strategy Id"}, "description": "ID of the strategy"}, {"name": "expiry_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ExpiryType"}, {"type": "null"}], "description": "Filter by expiry type", "title": "Expiry Type"}, "description": "Filter by expiry type"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of records to skip", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Number of records to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Number of records to return", "default": 100, "title": "Limit"}, "description": "Number of records to return"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/client-strategy-rules/{rule_id}": {"get": {"tags": ["client-strategy-rules"], "summary": "Get a rule by ID", "description": "Get a specific trading rule by its ID.", "operationId": "get_rule_api_v1_client_strategy_rules__rule_id__get", "parameters": [{"name": "rule_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Rule ID", "title": "Rule Id"}, "description": "Rule ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["client-strategy-rules"], "summary": "Update a rule", "description": "Update a trading rule.", "operationId": "update_rule_api_v1_client_strategy_rules__rule_id__put", "parameters": [{"name": "rule_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Rule ID", "title": "Rule Id"}, "description": "Rule ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClientStrategyRuleUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["client-strategy-rules"], "summary": "Delete a rule", "description": "Delete a trading rule.", "operationId": "delete_rule_api_v1_client_strategy_rules__rule_id__delete", "parameters": [{"name": "rule_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Rule ID", "title": "Rule Id"}, "description": "Rule ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/client-strategy-rules/validate": {"post": {"tags": ["client-strategy-rules"], "summary": "Validate a rule against a price", "description": "Validate if a rule should be triggered based on the current price.\nReturns calculated target, stop loss, and other relevant values.", "operationId": "validate_rule_api_v1_client_strategy_rules_validate_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RuleValidationRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/{order_id}": {"get": {"tags": ["orders"], "summary": "Get an order by ID", "description": "Get detailed information about a specific order.", "operationId": "get_order_api_v1_orders__order_id__get", "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Order ID", "title": "Order Id"}, "description": "Order ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/": {"get": {"tags": ["orders"], "summary": "List orders with filtering", "description": "List all orders with optional filtering and pagination.\n\n- **client_id**: Filter by client ID\n- **strategy_id**: Filter by strategy ID\n- **status**: Filter by order status\n- **symbol**: Filter by trading symbol\n- **variety**: Filter by order variety\n- **expiry_type**: Filter by expiry type\n- **start_date**: Filter orders created on or after this date\n- **end_date**: Filter orders created on or before this date\n- **skip**: Number of records to skip (pagination)\n- **limit**: Maximum number of records to return (pagination)", "operationId": "list_orders_api_v1_orders__get", "parameters": [{"name": "client_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by client ID", "title": "Client Id"}, "description": "Filter by client ID"}, {"name": "strategy_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by strategy ID", "title": "Strategy Id"}, "description": "Filter by strategy ID"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by order status", "title": "Status"}, "description": "Filter by order status"}, {"name": "symbol", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by trading symbol", "title": "Symbol"}, "description": "Filter by trading symbol"}, {"name": "variety", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by order variety", "title": "Variety"}, "description": "Filter by order variety"}, {"name": "expiry_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by expiry type", "title": "Expiry Type"}, "description": "Filter by expiry type"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Filter by start date (created_at >= start_date)", "title": "Start Date"}, "description": "Filter by start date (created_at >= start_date)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Filter by end date (created_at <= end_date)", "title": "End Date"}, "description": "Filter by end date (created_at <= end_date)"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of records to skip", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Number of records to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Number of records to return", "default": 100, "title": "Limit"}, "description": "Number of records to return"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/status/counts": {"get": {"tags": ["orders"], "summary": "Get order counts by status", "description": "Get counts of orders grouped by status.\n\n- **client_id**: Optional client ID to filter by\n- **strategy_id**: Optional strategy ID to filter by\n\nReturns a dictionary with status as keys and counts as values.", "operationId": "get_order_status_counts_api_v1_orders_status_counts_get", "parameters": [{"name": "client_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by client ID", "title": "Client Id"}, "description": "Filter by client ID"}, {"name": "strategy_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by strategy ID", "title": "Strategy Id"}, "description": "Filter by strategy ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/broker-audit/{audit_id}": {"get": {"tags": ["broker-audit"], "summary": "Get a broker audit entry by ID", "description": "Get detailed information about a specific broker audit entry.", "operationId": "get_broker_audit_api_v1_broker_audit__audit_id__get", "parameters": [{"name": "audit_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Audit Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/broker-audit/": {"get": {"tags": ["broker-audit"], "summary": "List broker audit entries with filtering", "description": "List all broker audit entries with optional filtering and pagination.\n\n- **start_date**: Filter entries on or after this date\n- **end_date**: Filter entries on or before this date\n- **client_id**: Filter by client ID\n- **strategy_id**: Filter by strategy ID\n- **status**: Filter by status\n- **broker**: Filter by broker name\n- **skip**: Number of records to skip (pagination)\n- **limit**: Maximum number of records to return (pagination)", "operationId": "list_broker_audits_api_v1_broker_audit__get", "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Filter by start date (inclusive)", "title": "Start Date"}, "description": "Filter by start date (inclusive)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Filter by end date (inclusive)", "title": "End Date"}, "description": "Filter by end date (inclusive)"}, {"name": "client_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by client ID", "title": "Client Id"}, "description": "Filter by client ID"}, {"name": "strategy_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by strategy ID", "title": "Strategy Id"}, "description": "Filter by strategy ID"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by status", "title": "Status"}, "description": "Filter by status"}, {"name": "broker", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by broker name", "title": "Broker"}, "description": "Filter by broker name"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of records to skip", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Number of records to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Number of records to return", "default": 100, "title": "Limit"}, "description": "Number of records to return"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/broker-audit/status/counts": {"get": {"tags": ["broker-audit"], "summary": "Get broker audit status counts", "description": "Get counts of broker audit entries grouped by status.\n\n- **client_id**: Optional client ID to filter by\n- **broker**: Optional broker name to filter by\n\nReturns a dictionary with status as keys and counts as values.", "operationId": "get_broker_audit_status_counts_api_v1_broker_audit_status_counts_get", "parameters": [{"name": "client_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by client ID", "title": "Client Id"}, "description": "Filter by client ID"}, {"name": "broker", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by broker name", "title": "Broker"}, "description": "Filter by broker name"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/strategy-audit/{audit_id}": {"get": {"tags": ["strategy-audit"], "summary": "Get a strategy audit entry by ID", "description": "Get detailed information about a specific strategy audit entry.", "operationId": "get_strategy_audit_api_v1_strategy_audit__audit_id__get", "parameters": [{"name": "audit_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Audit Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/strategy-audit/": {"get": {"tags": ["strategy-audit"], "summary": "List strategy audit entries with filtering", "description": "List all strategy audit entries with optional filtering and pagination.\n\n- **start_date**: Filter entries on or after this date\n- **end_date**: Filter entries on or before this date\n- **strategy_name**: Filter by strategy name\n- **symbol**: Filter by trading symbol\n- **status**: Filter by status\n- **side**: Filter by order side (BUY/SELL)\n- **strategy_valid**: Filter by strategy validity\n- **skip**: Number of records to skip (pagination)\n- **limit**: Maximum number of records to return (pagination)", "operationId": "list_strategy_audits_api_v1_strategy_audit__get", "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Filter by start date (inclusive)", "title": "Start Date"}, "description": "Filter by start date (inclusive)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Filter by end date (inclusive)", "title": "End Date"}, "description": "Filter by end date (inclusive)"}, {"name": "strategy_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by strategy name", "title": "Strategy Name"}, "description": "Filter by strategy name"}, {"name": "symbol", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by trading symbol", "title": "Symbol"}, "description": "Filter by trading symbol"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by status", "title": "Status"}, "description": "Filter by status"}, {"name": "side", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/OrderSide"}, {"type": "null"}], "description": "Filter by order side (BUY/SELL)", "title": "Side"}, "description": "Filter by order side (BUY/SELL)"}, {"name": "strategy_valid", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by strategy validity", "title": "Strategy Valid"}, "description": "Filter by strategy validity"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of records to skip", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Number of records to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Number of records to return", "default": 100, "title": "Limit"}, "description": "Number of records to return"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/strategy-audit/status/counts": {"get": {"tags": ["strategy-audit"], "summary": "Get strategy audit status counts", "description": "Get counts of strategy audit entries grouped by status.\n\n- **strategy_name**: Optional strategy name to filter by\n- **strategy_valid**: Optional boolean to filter by strategy validity\n\nReturns a dictionary with status as keys and counts as values.", "operationId": "get_strategy_audit_status_counts_api_v1_strategy_audit_status_counts_get", "parameters": [{"name": "strategy_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by strategy name", "title": "Strategy Name"}, "description": "Filter by strategy name"}, {"name": "strategy_valid", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by strategy validity", "title": "Strategy Valid"}, "description": "Filter by strategy validity"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/health": {"get": {"summary": "Health Check", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"Broker": {"type": "string", "enum": ["zerodha", "angleone", "fyers", "aliceblue", "upstox", "finvasia", "fyers_enterprise", "flattrade"], "title": "Broker"}, "ClientCreate": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 2, "title": "Name", "description": "<PERSON><PERSON>'s full name"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Client's email address"}, "broker": {"$ref": "#/components/schemas/Broker", "description": "Broker platform"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether the client is active", "default": true}, "api_key": {"type": "string", "title": "Api Key", "description": "Broker API key"}, "api_secret": {"type": "string", "title": "Api Secret", "description": "Broker API secret"}, "user_id": {"type": "string", "minLength": 3, "title": "User Id", "description": "Broker user ID"}, "password": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Password", "description": "Broker password"}, "totp_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Totp Key", "description": "TOTP key for 2FA"}, "macaddress": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Macaddress", "description": "Client's MAC address"}, "local_ip": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Local Ip", "description": "Client's local IP address"}, "public_ip": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Public Ip", "description": "Client's public IP address"}}, "type": "object", "required": ["name", "broker", "api_key", "api_secret", "user_id"], "title": "ClientCreate", "description": "Model for creating a new client."}, "ClientStrategyCreate": {"properties": {"variety": {"$ref": "#/components/schemas/Variety", "description": "Strategy variety (NORMAL or ROBO)"}, "is_enabled": {"type": "boolean", "title": "Is Enabled", "description": "Whether the strategy is enabled for the client", "default": true}, "client_id": {"type": "string", "format": "uuid", "title": "Client Id", "description": "Client ID"}, "strategy_id": {"type": "string", "format": "uuid", "title": "Strategy Id", "description": "Strategy ID"}}, "type": "object", "required": ["variety", "client_id", "strategy_id"], "title": "ClientStrategyCreate", "description": "Model for creating a new client-strategy mapping."}, "ClientStrategyRuleBase": {"properties": {"price_min": {"type": "number", "title": "Price Min"}, "price_max": {"type": "number", "title": "Price Max"}, "target": {"type": "number", "title": "Target"}, "stop_loss": {"type": "number", "title": "Stop Loss"}, "trailing_sl": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Trailing Sl", "description": "Trailing stop loss in percentage/points (if applicable)"}, "quantity": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Quantity", "description": "Quantity to trade"}, "mode": {"$ref": "#/components/schemas/Mode", "description": "Whether target/sl is in percentage or points"}, "expiry_type": {"$ref": "#/components/schemas/ExpiryType", "description": "Type of expiry/instrument"}}, "type": "object", "required": ["price_min", "price_max", "target", "stop_loss", "quantity", "mode", "expiry_type"], "title": "ClientStrategyRuleBase", "description": "Base model for client strategy rule."}, "ClientStrategyRuleCreate": {"properties": {"price_min": {"type": "number", "title": "Price Min"}, "price_max": {"type": "number", "title": "Price Max"}, "target": {"type": "number", "title": "Target"}, "stop_loss": {"type": "number", "title": "Stop Loss"}, "trailing_sl": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Trailing Sl", "description": "Trailing stop loss in percentage/points (if applicable)"}, "quantity": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Quantity", "description": "Quantity to trade"}, "mode": {"$ref": "#/components/schemas/Mode", "description": "Whether target/sl is in percentage or points"}, "expiry_type": {"$ref": "#/components/schemas/ExpiryType", "description": "Type of expiry/instrument"}, "client_id": {"type": "string", "minLength": 1, "title": "Client Id", "description": "Client ID"}, "client_strategy_id": {"type": "string", "minLength": 1, "title": "Client Strategy Id", "description": "Client-Strategy mapping ID"}}, "type": "object", "required": ["price_min", "price_max", "target", "stop_loss", "quantity", "mode", "expiry_type", "client_id", "client_strategy_id"], "title": "ClientStrategyRuleCreate", "description": "Model for creating a new client strategy rule."}, "ClientStrategyRuleUpdate": {"properties": {"price_min": {"anyOf": [{"type": "number", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Price Min"}, "price_max": {"anyOf": [{"type": "number", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Price Max"}, "target": {"anyOf": [{"type": "number", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Target"}, "stop_loss": {"anyOf": [{"type": "number", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Stop Loss"}, "trailing_sl": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Trailing Sl"}, "quantity": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Quantity"}, "mode": {"anyOf": [{"$ref": "#/components/schemas/Mode"}, {"type": "null"}]}, "expiry_type": {"anyOf": [{"$ref": "#/components/schemas/ExpiryType"}, {"type": "null"}]}}, "type": "object", "title": "ClientStrategyRuleUpdate", "description": "Model for updating a client strategy rule."}, "ClientStrategyUpdate": {"properties": {"variety": {"anyOf": [{"$ref": "#/components/schemas/Variety"}, {"type": "null"}]}, "is_enabled": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Enabled"}}, "type": "object", "title": "ClientStrategyUpdate", "description": "Model for updating a client-strategy mapping."}, "ClientUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 2}, {"type": "null"}], "title": "Name"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "broker": {"anyOf": [{"$ref": "#/components/schemas/Broker"}, {"type": "null"}]}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}, "api_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Key"}, "api_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Secret"}, "password": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Password"}, "totp_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Totp Key"}, "macaddress": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Macaddress"}, "local_ip": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Local Ip"}, "public_ip": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Public Ip"}}, "type": "object", "title": "ClientUpdate", "description": "Model for updating an existing client."}, "ExpiryType": {"type": "string", "enum": ["CE", "PE", "FUT", "EQ", "OPT"], "title": "ExpiryType", "description": "Expiry type for the rule."}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "Mode": {"type": "string", "enum": ["percent", "points"], "title": "Mode", "description": "Calculation mode for target/stop loss."}, "OrderSide": {"type": "string", "enum": ["BUY", "SELL"], "title": "OrderSide", "description": "Order side enumeration."}, "RuleValidationRequest": {"properties": {"price": {"type": "number", "title": "Price"}, "rule": {"$ref": "#/components/schemas/ClientStrategyRuleBase"}}, "type": "object", "required": ["price", "rule"], "title": "RuleValidationRequest", "description": "Model for validating a rule against a price."}, "StrategyCreate": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 2, "title": "Name", "description": "Strategy name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Strategy description"}, "parameters": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parameters", "description": "Strategy parameters in JSON string format"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether the strategy is active", "default": true}}, "type": "object", "required": ["name"], "title": "StrategyCreate", "description": "Model for creating a new strategy."}, "StrategyResponse": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 2, "title": "Name", "description": "Strategy name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Strategy description"}, "parameters": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parameters", "description": "Strategy parameters in JSON string format"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether the strategy is active", "default": true}, "id": {"type": "string", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["name", "id", "created_at", "updated_at"], "title": "StrategyResponse", "description": "Response model for strategy data."}, "StrategyUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 2}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "parameters": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parameters"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}, "type": "object", "title": "StrategyUpdate", "description": "Model for updating an existing strategy."}, "UserCreate": {"properties": {"email": {"type": "string", "format": "email", "title": "Email", "description": "User's email address"}, "full_name": {"type": "string", "title": "Full Name", "description": "User's full name"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether the user is active", "default": true}, "password": {"type": "string", "minLength": 8, "title": "Password", "description": "User's password (min 8 characters)"}}, "type": "object", "required": ["email", "full_name", "password"], "title": "UserCreate", "description": "Model for creating a new user."}, "UserResponse": {"properties": {"email": {"type": "string", "format": "email", "title": "Email", "description": "User's email address"}, "full_name": {"type": "string", "title": "Full Name", "description": "User's full name"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether the user is active", "default": true}, "id": {"type": "integer", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["email", "full_name", "id", "created_at", "updated_at"], "title": "UserResponse", "description": "Response model for user data (excludes sensitive information)."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "Variety": {"type": "string", "enum": ["NORMAL", "ROBO"], "title": "Variety", "description": "Variety of the strategy for a client."}}}}