
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { BarChart3, Users, BriefcaseBusiness, ShoppingCart, LayoutGrid, ScrollText, LogOut, GitBranch } from 'lucide-react';

interface NavLinkProps {
  to: string;
  icon: React.ReactNode;
  text: string;
  isActive: boolean;
}

const NavLink: React.FC<NavLinkProps> = ({ to, icon, text, isActive }) => {
  return (
    <Link
      to={to}
      className={`flex items-center px-4 py-3 mb-1 rounded-md transition-colors ${
        isActive ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent/50'
      }`}
    >
      <span className="mr-3">{icon}</span>
      <span>{text}</span>
    </Link>
  );
};

const Sidebar: React.FC = () => {
  const location = useLocation();
  const currentPath = location.pathname;
  
  return (
    <div className="bg-sidebar w-64 flex-shrink-0 hidden md:flex flex-col border-r border-sidebar-border">
      <div className="p-4 border-b border-sidebar-border">
        <h1 className="text-xl font-bold text-sidebar-foreground">Algo Bridge</h1>
      </div>
      <nav className="flex-1 p-4 space-y-1">
        <NavLink 
          to="/" 
          icon={<BarChart3 size={18} />} 
          text="Dashboard" 
          isActive={currentPath === '/'} 
        />
        <NavLink 
          to="/clients" 
          icon={<Users size={18} />} 
          text="Clients" 
          isActive={currentPath.startsWith('/clients')} 
        />
        <NavLink 
          to="/strategies" 
          icon={<BriefcaseBusiness size={18} />} 
          text="Strategies" 
          isActive={currentPath.startsWith('/strategies')} 
        />
        <NavLink 
          to="/client-strategies" 
          icon={<GitBranch size={18} />} 
          text="Client Strategies" 
          isActive={currentPath === '/client-strategies'} 
        />
        <NavLink 
          to="/place-order" 
          icon={<ShoppingCart size={18} />} 
          text="Place Order" 
          isActive={currentPath === '/place-order'} 
        />
        <NavLink 
          to="/orders" 
          icon={<ShoppingCart size={18} />} 
          text="Orders" 
          isActive={currentPath === '/orders'} 
        />
        <NavLink 
          to="/statistics" 
          icon={<LayoutGrid size={18} />} 
          text="Statistics" 
          isActive={currentPath === '/statistics'} 
        />
        
        {/* Updated Audit Trail navigation */}
        <NavLink 
          to="/broker-audit" 
          icon={<ScrollText size={18} />} 
          text="Broker Audit" 
          isActive={currentPath === '/broker-audit'} 
        />
        <NavLink 
          to="/signal-audit" 
          icon={<ScrollText size={18} />} 
          text="Signal Audit" 
          isActive={currentPath === '/signal-audit'} 
        />
      </nav>
      <div className="p-4 border-t border-sidebar-border">
        <button className="w-full flex items-center px-4 py-2 text-sidebar-foreground hover:bg-sidebar-accent/50 rounded-md transition-colors">
          <LogOut size={18} className="mr-3" />
          <span>Logout</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
