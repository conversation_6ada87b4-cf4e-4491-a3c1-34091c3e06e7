import React, { useState } from 'react';
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { createClient } from '@/api/apiService';
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from "@/components/ui/select";

const clientSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }).max(100),
  email: z.string().email({ message: "Please enter a valid email address" }).optional().or(z.literal("")),
  broker: z.enum(["zerodha", "angel_one", "fyers", "aliceblue", "upstox", "finvasia", "fyers_enterprise", "flattrade"]),
  api_key: z.string().min(2, { message: "API key must be at least 2 characters" }),
  api_secret: z.string().min(2, { message: "API secret must be at least 2 characters" }),
  user_id: z.string().min(3, { message: "User ID must be at least 3 characters" }),
  password: z.string().min(3, { message: "Password must be at least 3 characters" }).optional().or(z.literal("")),
  totp_key: z.string().optional().or(z.literal("")),
  macaddress: z.string().optional().or(z.literal("")),
  local_ip: z.string().optional().or(z.literal("")),
  public_ip: z.string().optional().or(z.literal("")),
  is_active: z.boolean(),
});

type ClientFormValues = z.infer<typeof clientSchema>;

interface AddClientDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddClientDialog: React.FC<AddClientDialogProps> = ({ isOpen, onClose, onSuccess }) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const form = useForm<ClientFormValues>({
    resolver: zodResolver(clientSchema),
    defaultValues: {
      name: "",
      email: "",
      broker: "zerodha",
      is_active: true,
      api_key: "",
      api_secret: "",
      user_id: "",
      password: "",
      totp_key: "",
      macaddress: "",
      local_ip: "",
      public_ip: "",
    },
  });

  const onSubmit = async (values: ClientFormValues) => {
    try {
      setLoading(true);
      
      // Ensure required fields are present
      const clientData = {
        name: values.name || "",
        email: values.email,
        broker: values.broker || "zerodha",
        is_active: values.is_active,
        api_key: values.api_key || "",
        api_secret: values.api_secret || "",
        user_id: values.user_id || "",
        password: values.password,
        totp_key: values.totp_key,
        macaddress: values.macaddress,
        local_ip: values.local_ip,
        public_ip: values.public_ip,
      };

      const response = await createClient(clientData);
      
      if (response.status === 'success') {
        toast({
          title: "Success",
          description: "Client created successfully",
        });
        form.reset();
        onSuccess();
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to create client",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        onClose();
        form.reset();
      }
    }}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add New Client</DialogTitle>
          <DialogDescription>
            Add a new client to the system.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter client name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter email address" type="email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="broker"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Broker</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select broker" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="zerodha">Zerodha</SelectItem>
                        <SelectItem value="angel_one">Angel One</SelectItem>
                        <SelectItem value="fyers">Fyers</SelectItem>
                        <SelectItem value="aliceblue">Alice Blue</SelectItem>
                        <SelectItem value="upstox">Upstox</SelectItem>
                        <SelectItem value="finvasia">Finvasia</SelectItem>
                        <SelectItem value="fyers_enterprise">Fyers Enterprise</SelectItem>
                        <SelectItem value="flattrade">Flattrade</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="user_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>User ID</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter user ID" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="api_key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Key</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter API key" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="api_secret"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Secret</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="Enter API secret" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="Enter password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="totp_key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>TOTP Key</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter TOTP key" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select 
                      onValueChange={(value) => field.onChange(value === "true")} 
                      defaultValue={field.value ? "true" : "false"}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="true">Active</SelectItem>
                        <SelectItem value="false">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <DialogFooter>
              <Button variant="outline" type="button" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Adding..." : "Add Client"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddClientDialog;
