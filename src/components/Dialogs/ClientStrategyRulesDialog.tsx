
import React, { useState, useEffect } from 'react';
import { useToast } from "@/hooks/use-toast";
import { ClientStrategyRule } from '@/api/types';
import { 
  getClientStrategyRules, 
  createClientStrategyRule, 
  updateClientStrategyRule, 
  deleteClientStrategyRule 
} from '@/services/ClientStrategyRulesService';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Edit, Trash2 } from 'lucide-react';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

const clientStrategyRuleSchema = z.object({
  price_min: z.number().min(0, "Price min must be positive"),
  price_max: z.number().min(0, "Price max must be positive"),
  target: z.number().min(0, "Target must be positive"),
  stop_loss: z.number().min(0, "Stop loss must be positive"),
  trailing_sl: z.number().optional(),
  quantity: z.number().int().min(1, "Quantity must be at least 1"),
  mode: z.enum(['percent', 'points']),
  expiry_type: z.enum(['CE', 'PE', 'FUT', 'EQ', 'OPT']),
});

type ClientStrategyRuleFormValues = z.infer<typeof clientStrategyRuleSchema>;

interface ClientStrategyRulesDialogProps {
  open: boolean;
  onClose: () => void;
  clientId: string;
  strategyId: string;
  clientName: string;
  strategyName: string;
}

const ClientStrategyRulesDialog: React.FC<ClientStrategyRulesDialogProps> = ({
  open,
  onClose,
  clientId,
  strategyId,
  clientName,
  strategyName
}) => {
  const [rules, setRules] = useState<ClientStrategyRule[]>([]);
  const [loading, setLoading] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<ClientStrategyRule | null>(null);
  const { toast } = useToast();

  const form = useForm<ClientStrategyRuleFormValues>({
    resolver: zodResolver(clientStrategyRuleSchema),
    defaultValues: {
      price_min: 0,
      price_max: 0,
      target: 0,
      stop_loss: 0,
      trailing_sl: undefined,
      quantity: 1,
      mode: "percent",
      expiry_type: "CE",
    },
  });

  useEffect(() => {
    if (open && clientId && strategyId) {
      fetchRules();
    }
  }, [open, clientId, strategyId]);

  const fetchRules = async () => {
    try {
      setLoading(true);
      const response = await getClientStrategyRules({ client_id: clientId, strategy_id: strategyId });
      if (response.status === 'success') {
        setRules(response.data.data);
      } else {
        toast({
          title: "Error",
          description: "Failed to load rules",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddRule = () => {
    setEditingRule(null);
    form.reset();
    setIsAddDialogOpen(true);
  };

  const handleEditRule = (rule: ClientStrategyRule) => {
    setEditingRule(rule);
    form.reset({
      price_min: rule.price_min,
      price_max: rule.price_max,
      target: rule.target,
      stop_loss: rule.stop_loss,
      trailing_sl: rule.trailing_sl || undefined,
      quantity: rule.quantity,
      mode: rule.mode,
      expiry_type: rule.expiry_type,
    });
    setIsAddDialogOpen(true);
  };

  const handleDeleteRule = async (rule: ClientStrategyRule) => {
    try {
      const response = await deleteClientStrategyRule(rule.id);
      if (response.status === 'success') {
        toast({
          title: "Success",
          description: "Rule deleted successfully",
        });
        fetchRules();
      } else {
        toast({
          title: "Error",
          description: "Failed to delete rule",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  const onSubmit = async (values: ClientStrategyRuleFormValues) => {
    try {
      if (editingRule) {
        const response = await updateClientStrategyRule(editingRule.id, values);
        if (response.status === 'success') {
          toast({
            title: "Success",
            description: "Rule updated successfully",
          });
        }
      } else {
        const ruleData = {
          ...values,
          client_id: clientId,
          strategy_id: strategyId,
        };
        const response = await createClientStrategyRule(clientId, strategyId, ruleData);
        if (response.status === 'success') {
          toast({
            title: "Success",
            description: "Rule created successfully",
          });
        }
      }
      
      setIsAddDialogOpen(false);
      fetchRules();
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[900px]">
          <DialogHeader>
            <DialogTitle>Strategy Rules</DialogTitle>
            <DialogDescription>
              Rules for {clientName} - {strategyName}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h4 className="font-medium">Current Rules</h4>
              <Button onClick={handleAddRule} size="sm">
                <Plus size={16} className="mr-2" /> Add Rule
              </Button>
            </div>
            
            {loading ? (
              <div className="text-center p-4">Loading rules...</div>
            ) : (
              <div className="table-container max-h-64 overflow-y-auto">
                <table className="data-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>MIN PRICE</th>
                      <th>MAX PRICE</th>
                      <th>TARGET</th>
                      <th>SL</th>
                      <th>TRAILING</th>
                      <th>MODE</th>
                      <th>EXPIRY TYPE</th>
                      <th>ACTIONS</th>
                    </tr>
                  </thead>
                  <tbody>
                    {rules.length > 0 ? rules.map((rule) => (
                      <tr key={rule.id}>
                        <td className="text-xs font-mono">{rule.id.substring(0, 8)}...</td>
                        <td>{rule.price_min}</td>
                        <td>{rule.price_max}</td>
                        <td>{rule.target}</td>
                        <td>{rule.stop_loss}</td>
                        <td>{rule.trailing_sl || '-'}</td>
                        <td>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            rule.mode === 'percent' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                          }`}>
                            {rule.mode}
                          </span>
                        </td>
                        <td>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            rule.expiry_type === 'CE' ? 'bg-green-100 text-green-800' :
                            rule.expiry_type === 'PE' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {rule.expiry_type}
                          </span>
                        </td>
                        <td>
                          <div className="flex space-x-1">
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="action-icon"
                              onClick={() => handleEditRule(rule)}
                            >
                              <Edit size={14} />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="action-icon"
                              onClick={() => handleDeleteRule(rule)}
                            >
                              <Trash2 size={14} />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    )) : (
                      <tr>
                        <td colSpan={9} className="text-center p-4 text-muted-foreground">
                          No rules found for this client strategy
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add/Edit Rule Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{editingRule ? 'Edit' : 'Add'} Rule</DialogTitle>
            <DialogDescription>
              {editingRule ? 'Update the rule details.' : 'Add a new rule for this client strategy.'}
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="price_min"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price Min</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" placeholder="0.00" {...field} onChange={e => field.onChange(parseFloat(e.target.value) || 0)} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="price_max"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price Max</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" placeholder="0.00" {...field} onChange={e => field.onChange(parseFloat(e.target.value) || 0)} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="target"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" placeholder="0.00" {...field} onChange={e => field.onChange(parseFloat(e.target.value) || 0)} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="stop_loss"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stop Loss</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" placeholder="0.00" {...field} onChange={e => field.onChange(parseFloat(e.target.value) || 0)} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="trailing_sl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Trailing SL (Optional)</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.01" placeholder="0.00" {...field} onChange={e => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="quantity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Quantity</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="1" {...field} onChange={e => field.onChange(parseInt(e.target.value) || 1)} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="mode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Mode</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select mode" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="percent">Percent</SelectItem>
                          <SelectItem value="points">Points</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="expiry_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Expiry Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select expiry type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="CE">CE (Call European)</SelectItem>
                          <SelectItem value="PE">PE (Put European)</SelectItem>
                          <SelectItem value="FUT">FUT (Future)</SelectItem>
                          <SelectItem value="EQ">EQ (Equity)</SelectItem>
                          <SelectItem value="OPT">OPT (Option)</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <DialogFooter>
                <Button variant="outline" type="button" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  {editingRule ? 'Update' : 'Create'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ClientStrategyRulesDialog;
