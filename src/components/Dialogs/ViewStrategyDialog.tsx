
import React from 'react';
import { Strategy } from '@/api/types';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";

interface ViewStrategyDialogProps {
  strategy: Strategy | null;
  isOpen: boolean;
  onClose: () => void;
}

const ViewStrategyDialog: React.FC<ViewStrategyDialogProps> = ({ strategy, isOpen, onClose }) => {
  if (!strategy) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Strategy Details</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-sm text-muted-foreground">Name</h3>
            <p className="text-base">{strategy.name}</p>
          </div>
          <Separator />
          <div>
            <h3 className="font-medium text-sm text-muted-foreground">Description</h3>
            <p className="text-base">{strategy.description}</p>
          </div>
          <Separator />
          <div>
            <h3 className="font-medium text-sm text-muted-foreground">Parameters</h3>
            <pre className="text-xs bg-muted p-2 rounded-md overflow-auto">
              {strategy.parameters}
            </pre>
          </div>
          <Separator />
          <div>
            <h3 className="font-medium text-sm text-muted-foreground">Status</h3>
            <span className={`status-${strategy.is_active ? 'active' : 'inactive'}`}>
              {strategy.is_active ? 'Active' : 'Inactive'}
            </span>
          </div>
          <Separator />
          <div>
            <h3 className="font-medium text-sm text-muted-foreground">Created</h3>
            <p className="text-base">{strategy.created_at}</p>
          </div>
          {strategy.updated_at && (
            <>
              <Separator />
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Last Updated</h3>
                <p className="text-base">{strategy.updated_at}</p>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ViewStrategyDialog;
