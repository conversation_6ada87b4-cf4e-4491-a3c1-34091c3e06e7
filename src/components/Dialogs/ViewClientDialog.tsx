
import React from 'react';
import { Client } from '@/api/types';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
} from "@/components/ui/card";

interface ViewClientDialogProps {
  isOpen: boolean;
  onClose: () => void;
  client: Client;
}

const ViewClientDialog: React.FC<ViewClientDialogProps> = ({ isOpen, onClose, client }) => {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Client Details</DialogTitle>
          <DialogDescription>
            View detailed information about this client.
          </DialogDescription>
        </DialogHeader>
        
        <Card className="border-0 shadow-none">
          <CardContent className="p-0 space-y-4">
            <div className="grid grid-cols-2 gap-4 py-2">
              <div>
                <p className="text-sm text-muted-foreground">Name</p>
                <p className="font-medium">{client.name}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Email</p>
                <p className="font-medium">{client.email || 'Not set'}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 py-2">
              <div>
                <p className="text-sm text-muted-foreground">Broker</p>
                <p className="font-medium">{client.broker}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">User ID</p>
                <p className="font-medium">{client.user_id || 'Not set'}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 py-2">
              <div>
                <p className="text-sm text-muted-foreground">Status</p>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  client.is_active === 1 ? 'bg-accent text-accent-foreground' : 'bg-destructive/20 text-destructive'
                }`}>
                  {client.is_active === 1 ? 'Active' : 'Inactive'}
                </span>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Created Date</p>
                <p className="font-medium">{new Date(client.created_at).toLocaleDateString()}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 py-2">
              <div>
                <p className="text-sm text-muted-foreground">API Key</p>
                <p className="font-medium font-mono text-xs bg-muted p-1 rounded overflow-x-auto">
                  {client.api_key ? client.api_key : 'Not set'}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">API Secret</p>
                <p className="font-medium font-mono text-xs bg-muted p-1 rounded overflow-x-auto">
                  {client.api_secret ? '•••••••••••••••' : 'Not set'}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 py-2">
              <div>
                <p className="text-sm text-muted-foreground">TOTP Key</p>
                <p className="font-medium">
                  {client.totp_key ? '•••••••••••••••' : 'Not set'}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">MAC Address</p>
                <p className="font-medium">{client.macaddress || 'Not set'}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <DialogFooter>
          <Button onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ViewClientDialog;
