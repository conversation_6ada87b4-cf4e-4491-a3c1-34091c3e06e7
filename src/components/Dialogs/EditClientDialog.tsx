
import React, { useState, useEffect } from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { updateClient } from '@/api/apiService';
import { Client } from '@/api/types';
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from "@/components/ui/select";

const clientSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }).max(100).optional(),
  email: z.string().email({ message: "Please enter a valid email address" }).optional(),
  broker: z.enum(["zerodha", "angel_one", "fyers", "aliceblue", "upstox", "finvasia", "fyers_enterprise", "flattrade"]).optional(),
  api_key: z.string().optional(),
  api_secret: z.string().optional(),
  password: z.string().optional(),
  totp_key: z.string().optional(),
  macaddress: z.string().optional(),
  local_ip: z.string().optional(),
  public_ip: z.string().optional(),
  is_active: z.boolean(),
});

type FormValues = z.infer<typeof clientSchema>;

interface EditClientDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  client: Client;
}

const EditClientDialog: React.FC<EditClientDialogProps> = ({ isOpen, onClose, onSuccess, client }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(clientSchema),
    defaultValues: {
      name: client.name,
      email: client.email || "",
      broker: client.broker as any,
      api_key: client.api_key || "",
      api_secret: client.api_secret || "",
      password: "",  // Don't pre-populate password for security
      totp_key: client.totp_key || "",
      macaddress: client.macaddress || "",
      local_ip: client.local_ip || "",
      public_ip: client.public_ip || "",
      is_active: client.is_active === 1,
    },
  });

  // Update form when client changes
  useEffect(() => {
    form.reset({
      name: client.name,
      email: client.email || "",
      broker: client.broker as any,
      api_key: client.api_key || "",
      api_secret: client.api_secret || "",
      password: "",  // Don't pre-populate password for security
      totp_key: client.totp_key || "",
      macaddress: client.macaddress || "",
      local_ip: client.local_ip || "",
      public_ip: client.public_ip || "",
      is_active: client.is_active === 1,
    });
  }, [client, form]);

  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true);
    try {
      const response = await updateClient(client.id, values);
      
      if (response.status === 'success') {
        toast.success("Client updated successfully");
        onSuccess();
      } else {
        toast.error(response.message || "Failed to update client");
      }
    } catch (error) {
      toast.error("An error occurred while updating the client");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        onClose();
      }
    }}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Client</DialogTitle>
          <DialogDescription>
            Update client information.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type="email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="broker"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Broker</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select broker" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="zerodha">Zerodha</SelectItem>
                        <SelectItem value="angel_one">Angel One</SelectItem>
                        <SelectItem value="fyers">Fyers</SelectItem>
                        <SelectItem value="aliceblue">Alice Blue</SelectItem>
                        <SelectItem value="upstox">Upstox</SelectItem>
                        <SelectItem value="finvasia">Finvasia</SelectItem>
                        <SelectItem value="fyers_enterprise">Fyers Enterprise</SelectItem>
                        <SelectItem value="flattrade">Flattrade</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select 
                      onValueChange={(value) => field.onChange(value === "true")} 
                      defaultValue={field.value ? "true" : "false"}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="true">Active</SelectItem>
                        <SelectItem value="false">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="api_key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Key</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter API key" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="api_secret"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Secret</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} placeholder="Enter API secret" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} placeholder="Enter new password" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="totp_key"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>TOTP Key</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter TOTP key" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <DialogFooter>
              <Button variant="outline" type="button" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Updating..." : "Update Client"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default EditClientDialog;
