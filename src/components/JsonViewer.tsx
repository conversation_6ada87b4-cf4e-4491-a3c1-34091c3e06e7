
import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Copy, Maximize } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface JsonViewerProps {
  data: string;
  maxHeight?: string;
}

const JsonViewer: React.FC<JsonViewerProps> = ({ data, maxHeight = '150px' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();
  
  const formattedJson = React.useMemo(() => {
    try {
      // First try to parse the data as JSON
      const parsed = JSON.parse(data);
      return JSON.stringify(parsed, null, 2);
    } catch (e) {
      // If parsing fails, check if it's already a string that looks like JSON
      try {
        // Try to parse it again in case it's double-encoded
        const doubleParsed = JSON.parse(JSON.parse(data));
        return JSON.stringify(doubleParsed, null, 2);
      } catch (e2) {
        // If all parsing fails, return the original data
        return data;
      }
    }
  }, [data]);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(formattedJson);
    toast({
      title: "Copied to clipboard",
      description: "The content has been copied to your clipboard",
    });
  };

  return (
    <div className="relative">
      <pre 
        className="bg-muted/50 p-3 rounded text-xs font-mono overflow-auto whitespace-pre-wrap" 
        style={{ maxHeight }}
      >
        {formattedJson}
      </pre>
      
      <div className="absolute top-2 right-2 flex space-x-2">
        <Button size="sm" variant="ghost" onClick={copyToClipboard}>
          <Copy size={14} />
        </Button>
        <Button size="sm" variant="ghost" onClick={() => setIsOpen(true)}>
          <Maximize size={14} />
        </Button>
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Full JSON Content</DialogTitle>
          </DialogHeader>
          <div className="overflow-auto max-h-[calc(80vh-100px)]">
            <pre className="p-4 bg-muted/50 rounded text-xs font-mono whitespace-pre-wrap">
              {formattedJson}
            </pre>
          </div>
          <div className="flex justify-end">
            <Button onClick={copyToClipboard}>
              <Copy size={14} className="mr-2" />
              Copy to Clipboard
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default JsonViewer;
