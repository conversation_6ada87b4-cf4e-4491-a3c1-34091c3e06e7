
import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, X, Filter, RefreshCw } from 'lucide-react';

interface FilterField {
  key: string;
  label: string;
  type: 'text' | 'select' | 'date';
  options?: { value: string; label: string }[];
}

interface DataTableFilterProps {
  fields: FilterField[];
  onFilter: (filters: Record<string, string>) => void;
  onReset: () => void;
}

const DataTableFilter: React.FC<DataTableFilterProps> = ({ fields, onFilter, onReset }) => {
  const [filters, setFilters] = useState<Record<string, string>>({});
  const [isExpanded, setIsExpanded] = useState(false);

  const handleChange = (key: string, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onFilter(filters);
  };

  const handleReset = () => {
    setFilters({});
    onReset();
  };

  return (
    <div className="mb-6 bg-card rounded-lg p-4 animate-fade-in">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium flex items-center">
          <Filter className="mr-2" size={18} />
          Filter Data
        </h3>
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? 'Simple Filter' : 'Advanced Filter'}
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleReset}
            className="text-destructive hover:text-destructive"
          >
            <X className="mr-1" size={16} /> Clear
          </Button>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {!isExpanded ? (
          <div className="flex space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search all fields..."
                className="pl-8"
                value={filters['search'] || ''}
                onChange={(e) => handleChange('search', e.target.value)}
              />
            </div>
            <Button type="submit" className="flex-shrink-0">
              Search
            </Button>
            <Button type="button" variant="outline" onClick={onReset} className="flex-shrink-0">
              <RefreshCw size={16} />
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {fields.map((field) => (
              <div key={field.key} className="space-y-1">
                <label htmlFor={field.key} className="text-sm font-medium">
                  {field.label}
                </label>
                {field.type === 'select' ? (
                  <select
                    id={field.key}
                    className="w-full rounded-md border border-border bg-card px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                    value={filters[field.key] || ''}
                    onChange={(e) => handleChange(field.key, e.target.value)}
                  >
                    <option value="">Any</option>
                    {field.options?.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                ) : field.type === 'date' ? (
                  <Input
                    id={field.key}
                    type="date"
                    value={filters[field.key] || ''}
                    onChange={(e) => handleChange(field.key, e.target.value)}
                  />
                ) : (
                  <Input
                    id={field.key}
                    type="text"
                    value={filters[field.key] || ''}
                    onChange={(e) => handleChange(field.key, e.target.value)}
                  />
                )}
              </div>
            ))}
            <div className="md:col-span-2 lg:col-span-3 flex justify-end space-x-2">
              <Button type="submit">
                Apply Filters
              </Button>
              <Button type="button" variant="outline" onClick={handleReset}>
                Reset
              </Button>
            </div>
          </div>
        )}
      </form>
    </div>
  );
};

export default DataTableFilter;
