import React, { useState, useMemo } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ChevronDown, ChevronRight, Maximize2, Copy } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface EnhancedJsonViewerProps {
  data: string;
  maxHeight?: string;
  showCopyButton?: boolean;
  showViewFullButton?: boolean;
  title?: string;
}

interface JsonNode {
  key: string;
  value: any;
  type: 'object' | 'array' | 'primitive';
  isExpanded: boolean;
  level: number;
}

const EnhancedJsonViewer: React.FC<EnhancedJsonViewerProps> = ({ 
  data, 
  maxHeight = '150px',
  showCopyButton = false,
  showViewFullButton = true,
  title = "JSON Content"
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const { toast } = useToast();

  const parsedData = useMemo(() => {
    try {
      const parsed = JSON.parse(data);
      return parsed;
    } catch (e) {
      try {
        const doubleParsed = JSON.parse(JSON.parse(data));
        return doubleParsed;
      } catch (e2) {
        return data;
      }
    }
  }, [data]);

  const formattedJson = useMemo(() => {
    try {
      return JSON.stringify(parsedData, null, 2);
    } catch (e) {
      return data;
    }
  }, [parsedData, data]);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(formattedJson);
    toast({
      title: "Copied to clipboard",
      description: "The JSON content has been copied to your clipboard",
    });
  };

  const toggleNode = (path: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedNodes(newExpanded);
  };

  const renderCollapsibleJson = (obj: any, path: string = '', level: number = 0): React.ReactNode => {
    if (obj === null || obj === undefined) {
      return <span className="text-gray-500">null</span>;
    }

    if (typeof obj === 'string') {
      return <span className="text-green-600">"{obj}"</span>;
    }

    if (typeof obj === 'number' || typeof obj === 'boolean') {
      return <span className="text-blue-600">{String(obj)}</span>;
    }

    if (Array.isArray(obj)) {
      const isExpanded = expandedNodes.has(path);
      const isEmpty = obj.length === 0;

      return (
        <div className="inline-block">
          <div className="flex items-center">
            {!isEmpty && (
              <button
                onClick={() => toggleNode(path)}
                className="mr-1 p-0.5 hover:bg-gray-200 rounded"
              >
                {isExpanded ? <ChevronDown size={12} /> : <ChevronRight size={12} />}
              </button>
            )}
            <span className="text-gray-600">[</span>
            {isEmpty && <span className="text-gray-600">]</span>}
          </div>
          {!isEmpty && isExpanded && (
            <div className="ml-4 border-l border-gray-200 pl-2">
              {obj.map((item, index) => (
                <div key={index} className="py-0.5">
                  <span className="text-gray-500 text-xs mr-2">{index}:</span>
                  {renderCollapsibleJson(item, `${path}[${index}]`, level + 1)}
                  {index < obj.length - 1 && <span className="text-gray-600">,</span>}
                </div>
              ))}
            </div>
          )}
          {!isEmpty && <span className="text-gray-600">]</span>}
        </div>
      );
    }

    if (typeof obj === 'object') {
      const keys = Object.keys(obj);
      const isExpanded = expandedNodes.has(path);
      const isEmpty = keys.length === 0;

      return (
        <div className="inline-block">
          <div className="flex items-center">
            {!isEmpty && (
              <button
                onClick={() => toggleNode(path)}
                className="mr-1 p-0.5 hover:bg-gray-200 rounded"
              >
                {isExpanded ? <ChevronDown size={12} /> : <ChevronRight size={12} />}
              </button>
            )}
            <span className="text-gray-600">{'{'}</span>
            {isEmpty && <span className="text-gray-600">{'}'}</span>}
          </div>
          {!isEmpty && isExpanded && (
            <div className="ml-4 border-l border-gray-200 pl-2">
              {keys.map((key, index) => (
                <div key={key} className="py-0.5">
                  <span className="text-purple-600 font-medium">"{key}"</span>
                  <span className="text-gray-600">: </span>
                  {renderCollapsibleJson(obj[key], `${path}.${key}`, level + 1)}
                  {index < keys.length - 1 && <span className="text-gray-600">,</span>}
                </div>
              ))}
            </div>
          )}
          {!isEmpty && <span className="text-gray-600">{'}'}</span>}
        </div>
      );
    }

    return <span>{String(obj)}</span>;
  };

  const isLargeJson = formattedJson.length > 500;

  return (
    <div className="relative">
      <div 
        className="bg-muted/50 p-3 rounded text-xs font-mono overflow-auto" 
        style={{ maxHeight }}
      >
        {isLargeJson ? (
          // For large JSON, show collapsible view
          <div className="whitespace-pre-wrap">
            {renderCollapsibleJson(parsedData)}
          </div>
        ) : (
          // For small JSON, show syntax highlighted view
          <SyntaxHighlighter
            language="json"
            style={vscDarkPlus}
            customStyle={{
              background: 'transparent',
              padding: 0,
              margin: 0,
              fontSize: '0.75rem',
              lineHeight: '1.2'
            }}
            wrapLongLines={true}
          >
            {formattedJson}
          </SyntaxHighlighter>
        )}
      </div>
      
      {(showCopyButton || showViewFullButton) && (
        <div className="absolute top-2 right-2 flex space-x-1">
          {showCopyButton && (
            <Button size="sm" variant="ghost" onClick={copyToClipboard}>
              <Copy size={12} />
            </Button>
          )}
          {showViewFullButton && (
            <Button size="sm" variant="ghost" onClick={() => setIsModalOpen(true)}>
              <Maximize2 size={12} />
            </Button>
          )}
        </div>
      )}

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>
          <div className="overflow-auto max-h-[calc(90vh-120px)]">
            <SyntaxHighlighter
              language="json"
              style={vscDarkPlus}
              customStyle={{
                margin: 0,
                fontSize: '0.875rem',
                lineHeight: '1.4'
              }}
              showLineNumbers={true}
              wrapLongLines={true}
            >
              {formattedJson}
            </SyntaxHighlighter>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={() => setIsModalOpen(false)}>
              Close
            </Button>
            <Button onClick={copyToClipboard}>
              <Copy size={14} className="mr-2" />
              Copy to Clipboard
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EnhancedJsonViewer;
