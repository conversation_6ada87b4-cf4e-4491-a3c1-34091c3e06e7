
import React from 'react';
import { Building2, Banknote, TrendingUp, Zap, BarChart3, Wallet, Globe, DollarSign } from 'lucide-react';

// Broker logo paths in public directory
const brokerIcons = {
  angelone: '/icons/angelone.png',
  flattrade: '/icons/flattrade.png',
};

interface BrokerIconProps {
  broker: string;
  size?: number;
  className?: string;
  showLabel?: boolean;
  variant?: 'icon' | 'chip';
}

// Broker logo mapping
const brokerLogos: Record<string, string> = {
  'angel_one': brokerIcons.angelone,
  'angelone': brokerIcons.angelone,
  'angelOne': brokerIcons.angelone,
  'angel one': brokerIcons.angelone,
  'flattrade': brokerIcons.flattrade,
  'flat_trade': brokerIcons.flattrade,
  'flat trade': brokerIcons.flattrade,
};

// Broker display names
const brokerDisplayNames: Record<string, string> = {
  'angel_one': 'Angel One',
  'angelone': 'Angel One',
  'angel one': 'Angel One',
  'flattrade': 'Flattrade',
  'flat_trade': 'Flattrade',
  'flat trade': 'Flattrade',
  'zerodha': 'Zerodha',
  'fyers': 'Fyers',
  'upstox': 'Upstox',
  'aliceblue': 'Alice Blue',
  'alice_blue': 'Alice Blue',
  'alice blue': 'Alice Blue',
  'finvasia': 'Finvasia',
  'fyers_enterprise': 'Fyers Enterprise',
};

// Broker colors for chip variant
const brokerColors: Record<string, string> = {
  'angel_one': 'bg-red-100 text-red-800',
  'angelone': 'bg-red-100 text-red-800',
  'angel one': 'bg-red-100 text-red-800',
  'flattrade': 'bg-green-100 text-green-800',
  'flat_trade': 'bg-green-100 text-green-800',
  'flat trade': 'bg-green-100 text-green-800',
  'zerodha': 'bg-blue-100 text-blue-800',
  'fyers': 'bg-purple-100 text-purple-800',
  'upstox': 'bg-orange-100 text-orange-800',
  'aliceblue': 'bg-cyan-100 text-cyan-800',
  'alice_blue': 'bg-cyan-100 text-cyan-800',
  'alice blue': 'bg-cyan-100 text-cyan-800',
  'finvasia': 'bg-yellow-100 text-yellow-800',
  'fyers_enterprise': 'bg-teal-100 text-teal-800',
};

const BrokerIcon: React.FC<BrokerIconProps> = ({
  broker,
  size = 16,
  className = "",
  showLabel = false,
  variant = 'icon'
}) => {
  const normalizedBroker = broker.toLowerCase().trim();
  const logoSrc = brokerLogos[normalizedBroker];
  const displayName = brokerDisplayNames[normalizedBroker] || broker;
  const colorClass = brokerColors[normalizedBroker] || 'bg-gray-100 text-gray-800';

  const getBrokerFallbackIcon = (brokerName: string) => {
    const props = { size, className };

    switch (brokerName.toLowerCase()) {
      case 'zerodha':
        return <TrendingUp {...props} className={`text-blue-600 ${className}`} />;
      case 'angel_one':
      case 'angelone':
      case 'angel one':
        return <Zap {...props} className={`text-red-600 ${className}`} />;
      case 'fyers':
        return <BarChart3 {...props} className={`text-purple-600 ${className}`} />;
      case 'aliceblue':
      case 'alice_blue':
      case 'alice blue':
        return <Wallet {...props} className={`text-cyan-600 ${className}`} />;
      case 'upstox':
        return <DollarSign {...props} className={`text-orange-600 ${className}`} />;
      case 'finvasia':
        return <Banknote {...props} className={`text-yellow-600 ${className}`} />;
      case 'fyers_enterprise':
        return <Globe {...props} className={`text-teal-600 ${className}`} />;
      case 'flattrade':
      case 'flat_trade':
      case 'flat trade':
        return <Building2 {...props} className={`text-green-600 ${className}`} />;
      default:
        return <Building2 {...props} className={`text-gray-600 ${className}`} />;
    }
  };

  if (variant === 'chip') {
    return (
      <span className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${colorClass} ${className}`}>
        {logoSrc ? (
          <img
            src={logoSrc}
            alt={`${displayName} logo`}
            className="w-4 h-4 object-contain"
          />
        ) : (
          getBrokerFallbackIcon(broker)
        )}
        {showLabel && <span>{displayName}</span>}
      </span>
    );
  }

  // Icon variant
  if (logoSrc) {
    return (
      <img
        src={logoSrc}
        alt={`${displayName} logo`}
        className={`object-contain ${className}`}
        style={{ width: size, height: size }}
      />
    );
  }

  return getBrokerFallbackIcon(broker);
};

// Utility functions
export const getBrokerDisplayName = (broker: string): string => {
  const normalizedBroker = broker.toLowerCase().trim();
  return brokerDisplayNames[normalizedBroker] || broker;
};

export const hasBrokerLogo = (broker: string): boolean => {
  const normalizedBroker = broker.toLowerCase().trim();
  return !!brokerLogos[normalizedBroker];
};

export const getBrokerColor = (broker: string): string => {
  const normalizedBroker = broker.toLowerCase().trim();
  return brokerColors[normalizedBroker] || 'bg-gray-100 text-gray-800';
};

export default BrokerIcon;
