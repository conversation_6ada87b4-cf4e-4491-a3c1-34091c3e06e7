
import React, { useState } from 'react';
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { ApiConfig } from '@/api/config';
import { useToast } from "@/hooks/use-toast";

const ApiToggle: React.FC = () => {
  const [useLiveApi, setUseLiveApi] = useState(!ApiConfig.useMockData);
  const { toast } = useToast();

  const handleToggleChange = () => {
    const newValue = ApiConfig.toggleMockData();
    setUseLiveApi(!newValue);
    
    toast({
      title: newValue ? "Using Mock Data" : "Using Live API",
      description: newValue
        ? "Switched to mock data mode"
        : "Attempting to connect to API server at http://localhost:8000. Note: If API returns HTML instead of JSON, the server may not be configured correctly.",
    });
  };

  return (
    <div className="flex items-center space-x-2">
      <Switch
        id="api-toggle"
        checked={useLiveApi}
        onCheckedChange={handleToggleChange}
      />
      <Label htmlFor="api-toggle">
        {useLiveApi ? "Using Live API" : "Using Mock Data"}
      </Label>
    </div>
  );
};

export default ApiToggle;
