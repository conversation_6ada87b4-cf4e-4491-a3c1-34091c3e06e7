
import { ApiResponse, PaginatedResponse, ClientStrategyRule } from '@/api/types';
import { ApiConfig } from '@/api/config';
import { apiGet, apiPost, apiPut, apiDelete } from '@/api/apiUtils';

// Get client strategy rules using the new endpoint pattern
export const getClientStrategyRules = async (params: {
  client_id: string;
  strategy_id: string;
}): Promise<ApiResponse<PaginatedResponse<ClientStrategyRule>>> => {
  if (ApiConfig.useMockData) {
    // Mock data for rules
    const mockRules: ClientStrategyRule[] = [
      {
        id: 'rule1',
        client_id: params.client_id,
        client_strategy_id: `${params.client_id}-${params.strategy_id}`,
        price_min: 100,
        price_max: 200,
        target: 150,
        stop_loss: 90,
        trailing_sl: 5,
        quantity: 10,
        mode: 'percent',
        expiry_type: 'CE',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }
    ];
    return {
      status: 'success',
      data: {
        data: mockRules,
        total: mockRules.length,
        page: 1,
        limit: 50,
        totalPages: 1
      }
    };
  } else {
    try {
      const response = await apiGet<ClientStrategyRule[]>(`/api/v1/client-strategy-rules/${params.client_id}/${params.strategy_id}`);
      if (response.status === 'success' && response.data) {
        return {
          status: 'success',
          data: {
            data: response.data,
            total: response.data.length,
            page: 1,
            limit: 50,
            totalPages: 1
          }
        };
      } else {
        return {
          status: 'error',
          message: response.message || 'Failed to get client strategy rules',
          data: {
            data: [],
            total: 0,
            page: 1,
            limit: 50,
            totalPages: 0
          }
        };
      }
    } catch (error) {
      console.error('Error getting client strategy rules:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {
          data: [],
          total: 0,
          page: 1,
          limit: 50,
          totalPages: 0
        }
      };
    }
  }
};

// Create client strategy rule using the new endpoint pattern
export const createClientStrategyRule = async (
  clientId: string,
  strategyId: string,
  rule: Omit<ClientStrategyRule, 'id' | 'created_at' | 'updated_at'>
): Promise<ApiResponse<ClientStrategyRule>> => {
  if (ApiConfig.useMockData) {
    const newRule = {
      ...rule,
      id: Math.random().toString(36).substring(7),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    return { status: 'success', data: newRule as ClientStrategyRule };
  } else {
    try {
      return await apiPost<ClientStrategyRule>(`/api/v1/client-strategy-rules/${clientId}/${strategyId}`, rule);
    } catch (error) {
      console.error('Error creating client strategy rule:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: {} as ClientStrategyRule 
      };
    }
  }
};

// Update client strategy rule using rule ID
export const updateClientStrategyRule = async (
  ruleId: string,
  rule: Partial<ClientStrategyRule>
): Promise<ApiResponse<ClientStrategyRule>> => {
  if (ApiConfig.useMockData) {
    const updatedRule = {
      id: ruleId,
      ...rule,
      updated_at: new Date().toISOString()
    };
    return { status: 'success', data: updatedRule as ClientStrategyRule };
  } else {
    try {
      return await apiPut<ClientStrategyRule>(`/api/v1/client-strategy-rules/${ruleId}`, rule);
    } catch (error) {
      console.error('Error updating client strategy rule:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: {} as ClientStrategyRule 
      };
    }
  }
};

// Delete client strategy rule using rule ID
export const deleteClientStrategyRule = async (ruleId: string): Promise<ApiResponse<boolean>> => {
  if (ApiConfig.useMockData) {
    return { status: 'success', data: true };
  } else {
    try {
      const response = await apiDelete<boolean>(`/api/v1/client-strategy-rules/${ruleId}`);
      return response;
    } catch (error) {
      console.error('Error deleting client strategy rule:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: false 
      };
    }
  }
};
