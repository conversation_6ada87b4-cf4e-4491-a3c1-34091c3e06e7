
import { 
  ApiResponse, 
  PaginatedResponse, 
  ClientStrategy, 
  ClientStrategyRule 
} from '@/api/types';
import { ApiConfig } from '@/api/config';
import { mockClientStrategies, mockClientStrategyRules } from '@/api/mockData';

export const getClientStrategies = async (): Promise<ApiResponse<PaginatedResponse<ClientStrategy>>> => {
  if (ApiConfig.useMockData) {
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      status: 'success',
      data: {
        data: mockClientStrategies,
        total: mockClientStrategies.length,
        page: 1,
        limit: 50,
        totalPages: 1
      }
    };
  } else {
    try {
      const response = await fetch(`${ApiConfig.apiBaseUrl}/api/v1/client-strategies/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        return {
          status: 'error',
          message: `HTTP error! status: ${response.status}`,
          data: {
            data: [],
            total: 0,
            page: 1,
            limit: 50,
            totalPages: 0
          }
        };
      }

      // Check if response is HTML (indicating server is serving static files instead of API)
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('text/html')) {
        return {
          status: 'error',
          message: 'API server is returning HTML instead of JSON. Please check if the backend API server is running correctly.',
          data: {
            data: [],
            total: 0,
            page: 1,
            limit: 50,
            totalPages: 0
          }
        };
      }

      const data = await response.json();
      return {
        status: 'success',
        data: {
          data: data.data || [],
          total: data.total || 0,
          page: data.page || 1,
          limit: data.limit || 50,
          totalPages: data.total_pages || 1
        }
      };
    } catch (error) {
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {
          data: [],
          total: 0,
          page: 1,
          limit: 50,
          totalPages: 0
        }
      };
    }
  }
};

export const getClientStrategyRules = async (clientStrategyId?: string): Promise<ApiResponse<PaginatedResponse<ClientStrategyRule>>> => {
  if (ApiConfig.useMockData) {
    await new Promise(resolve => setTimeout(resolve, 500));
    let filteredRules = mockClientStrategyRules;
    if (clientStrategyId) {
      filteredRules = mockClientStrategyRules.filter(rule => rule.client_strategy_id === clientStrategyId);
    }
    return {
      status: 'success',
      data: {
        data: filteredRules,
        total: filteredRules.length,
        page: 1,
        limit: 50,
        totalPages: 1
      }
    };
  } else {
    try {
      let url = `${ApiConfig.apiBaseUrl}/api/v1/client-strategy-rules`;
      if (clientStrategyId) {
        url += `?client_strategy_id=${clientStrategyId}`;
      }
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        return {
          status: 'error',
          message: `HTTP error! status: ${response.status}`,
          data: {
            data: [],
            total: 0,
            page: 1,
            limit: 50,
            totalPages: 0
          }
        };
      }
      
      const data = await response.json();
      return {
        status: 'success',
        data: {
          data: data.data || [],
          total: data.total || 0,
          page: data.page || 1,
          limit: data.limit || 50,
          totalPages: data.total_pages || 1
        }
      };
    } catch (error) {
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {
          data: [],
          total: 0,
          page: 1,
          limit: 50,
          totalPages: 0
        }
      };
    }
  }
};

export const createClientStrategy = async (
  clientStrategy: Omit<ClientStrategy, 'created_at' | 'updated_at'>
): Promise<ApiResponse<ClientStrategy>> => {
  if (ApiConfig.useMockData) {
    await new Promise(resolve => setTimeout(resolve, 500));
    const newClientStrategy = {
      ...clientStrategy,
      preference_type: clientStrategy.preference_type || 'AS_RECEIVED',
      preferred_instrument: clientStrategy.preferred_instrument || 'ANY',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    return { 
      status: 'success', 
      data: newClientStrategy 
    };
  } else {
    try {
      const response = await fetch(`${ApiConfig.apiBaseUrl}/api/v1/client-strategies/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(clientStrategy),
      });
      
      if (!response.ok) {
        return { 
          status: 'error', 
          message: `HTTP error! status: ${response.status}`,
          data: {} as ClientStrategy 
        };
      }
      
      const data = await response.json();
      return {
        status: 'success',
        data: data.data || data
      };
    } catch (error) {
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {} as ClientStrategy
      };
    }
  }
};

export const updateClientStrategy = async (
  clientId: string,
  strategyId: string,
  updates: Partial<ClientStrategy>
): Promise<ApiResponse<ClientStrategy>> => {
  if (ApiConfig.useMockData) {
    await new Promise(resolve => setTimeout(resolve, 500));
    return {
      status: 'success',
      data: {
        ...updates,
        client_id: clientId,
        strategy_id: strategyId,
        updated_at: new Date().toISOString()
      } as ClientStrategy
    };
  } else {
    try {
      const response = await fetch(`${ApiConfig.apiBaseUrl}/api/v1/client-strategies/${clientId}/${strategyId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });
      
      if (!response.ok) {
        return {
          status: 'error',
          message: `HTTP error! status: ${response.status}`,
          data: {} as ClientStrategy
        };
      }
      
      const data = await response.json();
      return {
        status: 'success',
        data: data.data || data
      };
    } catch (error) {
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {} as ClientStrategy
      };
    }
  }
};

export const deleteClientStrategy = async (
  clientId: string,
  strategyId: string
): Promise<ApiResponse<boolean>> => {
  if (ApiConfig.useMockData) {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { status: 'success', data: true };
  } else {
    try {
      const response = await fetch(`${ApiConfig.apiBaseUrl}/api/v1/client-strategies/${clientId}/${strategyId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        return {
          status: 'error',
          message: `HTTP error! status: ${response.status}`,
          data: false
        };
      }
      
      return { status: 'success', data: true };
    } catch (error) {
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: false
      };
    }
  }
};
