/**
 * Order Configuration
 * This file contains configurable settings for order management
 */

/**
 * Order statuses that allow modification and cancellation
 * These are the statuses where orders are considered "open" and can be modified/cancelled
 */
export const MODIFIABLE_ORDER_STATUSES = [
  'OPEN',
  'PENDING',
  'TRIGGER_PENDING',
  'PARTIALLY_FILLED',
  'PLACED',
  'VALIDATED',
  'QUEUED',
  'SUBMITTED',
  'ACTIVE'
] as const;

/**
 * Order statuses that are considered "closed" and cannot be modified/cancelled
 */
export const CLOSED_ORDER_STATUSES = [
  'COMPLETE',
  'COMPLETED',
  'FILLED',
  'CANCELLED',
  'CANCELED',
  'REJECTED',
  'EXPIRED',
  'FAILED'
] as const;

/**
 * Check if an order status allows modification/cancellation
 * @param status - The order status to check
 * @returns boolean - True if the order can be modified/cancelled
 */
export const isOrderModifiable = (status: string): boolean => {
  if (!status) return false;
  
  const normalizedStatus = status.toUpperCase().trim();
  return MODIFIABLE_ORDER_STATUSES.includes(normalizedStatus as any);
};

/**
 * Check if an order status is closed
 * @param status - The order status to check
 * @returns boolean - True if the order is closed
 */
export const isOrderClosed = (status: string): boolean => {
  if (!status) return false;
  
  const normalizedStatus = status.toUpperCase().trim();
  return CLOSED_ORDER_STATUSES.includes(normalizedStatus as any);
};

/**
 * Get all modifiable order statuses
 * @returns Array of modifiable order statuses
 */
export const getModifiableStatuses = (): readonly string[] => {
  return MODIFIABLE_ORDER_STATUSES;
};

/**
 * Get all closed order statuses
 * @returns Array of closed order statuses
 */
export const getClosedStatuses = (): readonly string[] => {
  return CLOSED_ORDER_STATUSES;
};
