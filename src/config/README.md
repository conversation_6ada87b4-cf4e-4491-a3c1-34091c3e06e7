# Order Configuration

This directory contains configuration files for order management functionality.

## Order Status Configuration (`orderConfig.ts`)

The `orderConfig.ts` file contains configurable settings for determining which order statuses allow modification and cancellation.

### Modifiable Order Statuses

Orders with the following statuses can be modified or cancelled:

- `O<PERSON>EN` - Order is open and waiting to be filled
- `PENDING` - Order is pending execution
- `TRIGGER_PENDING` - Order is waiting for trigger condition
- `PARTIALLY_FILLED` - Order is partially executed
- `PLACED` - Order has been placed
- `VALIDATED` - Order has been validated
- `QUEUED` - Order is in queue for execution
- `SUBMITTED` - Order has been submitted
- `ACTIVE` - Order is active

### Closed Order Statuses

Orders with the following statuses cannot be modified or cancelled:

- `COMPLETE` / `COMPLETED` - Order is fully executed
- `FILLED` - Order is completely filled
- `CANCELLED` / `CANCELED` - Order has been cancelled
- `REJECTED` - Order was rejected
- `EXPIRED` - Order has expired
- `FAILED` - Order execution failed

### How to Modify

To add or remove order statuses:

1. Open `src/config/orderConfig.ts`
2. Add new statuses to `M<PERSON><PERSON><PERSON>BLE_ORDER_STATUSES` array for statuses that should allow modification
3. Add new statuses to `CLOSED_ORDER_STATUSES` array for statuses that should not allow modification
4. The status comparison is case-insensitive and automatically trims whitespace

### Usage

The configuration is used in:
- **All Client Orders** table - Shows modify/cancel buttons only for modifiable orders
- **Client Orders** table - Shows modify/cancel buttons only for modifiable orders

The buttons will be disabled for orders with closed statuses, providing a consistent user experience across the application.
