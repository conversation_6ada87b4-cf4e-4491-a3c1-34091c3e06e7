
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 215 25% 27%;
    --foreground: 210 40% 98%;

    --card: 220 29% 20%;
    --card-foreground: 210 40% 98%;

    --popover: 220 29% 20%;
    --popover-foreground: 210 40% 98%;

    --primary: 208 76% 52%;
    --primary-foreground: 210 40% 98%;

    --secondary: 220 14% 90%;
    --secondary-foreground: 220 29% 20%;

    --muted: 220 14% 17%;
    --muted-foreground: 220 14% 70%;

    --accent: 142 76% 45%;
    --accent-foreground: 220 29% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 14% 30%;
    --input: 220 14% 30%;
    --ring: 215 25% 27%;

    --radius: 0.5rem;

    --sidebar-background: 220 33% 15%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 208 76% 52%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 220 14% 20%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 220 14% 25%;
    --sidebar-ring: 208 76% 52%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

.table-container {
  @apply w-full overflow-x-auto;
}

.data-table {
  @apply w-full border-collapse;
}

.data-table th {
  @apply bg-card text-left p-3 text-sm font-medium text-card-foreground uppercase tracking-wider;
}

.data-table td {
  @apply border-t border-border p-3 text-sm;
}

.data-table tr:hover {
  @apply bg-muted/30;
}

.status-active {
  @apply px-2 py-1 rounded-full text-xs font-medium bg-accent text-accent-foreground;
}

.status-complete {
  @apply px-2 py-1 rounded-full text-xs font-medium bg-accent text-accent-foreground;
}

.status-pending {
  @apply px-2 py-1 rounded-full text-xs font-medium bg-yellow-500 text-black;
}

.action-icon {
  @apply rounded-md p-2 hover:bg-muted transition-colors;
}

.filter-container {
  @apply flex flex-wrap gap-2 mb-4;
}

.filter-input {
  @apply bg-card border border-border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary;
}

.filter-button {
  @apply bg-primary text-primary-foreground rounded-md px-4 py-2 text-sm hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-primary transition-colors;
}

.refresh-button {
  @apply bg-muted text-muted-foreground rounded-md px-4 py-2 text-sm hover:bg-muted/80 focus:outline-none focus:ring-2 focus:ring-muted transition-colors;
}

.stats-card {
  @apply bg-card p-6 rounded-lg shadow-md flex flex-col;
}

.stats-card-icon {
  @apply w-12 h-12 text-primary mb-4 p-2 rounded-md bg-primary/20;
}

.stats-card-title {
  @apply text-sm font-medium text-muted-foreground;
}

.stats-card-value {
  @apply text-2xl font-bold mt-1;
}

.stats-card-link {
  @apply text-xs text-primary mt-4 hover:underline;
}

.tab-active {
  @apply border-b-2 border-primary text-primary;
}

.tab-inactive {
  @apply text-muted-foreground hover:text-foreground;
}
