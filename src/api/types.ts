// Client Types
export interface Client {
  id: string;
  name: string;
  email?: string;
  broker: string;
  api_key?: string;
  api_secret?: string;
  user_id?: string;
  password?: string;
  totp_key?: string;
  macaddress?: string;
  local_ip?: string;
  public_ip?: string;
  created_at: string;
  updated_at?: string;
  is_active: boolean; // UInt8 in ClickHouse
}

// Strategy Types
export interface Strategy {
  id: string;
  name: string;
  description: string;
  parameters: string;
  is_active: boolean;  // maps to is_active in the schema
  created_at: string;  // matches created_at in schema
  updated_at?: string; // matches updated_at in schema
}

// Client Strategy Types
export interface ClientStrategy {
  client_id: string;
  strategy_id: string;
  clientName?: string; // For UI display only
  strategyName?: string; // For UI display only
  variety: 'NORMAL' | 'ROBO';
  is_enabled: boolean;
  preference_type: 'AS_RECEIVED' | 'FORCE_BUY' | 'FORCE_SELL';
  preferred_instrument: 'ANY' | 'EQ' | 'FUTURE' | 'CE' | 'PE';
  rule_count?: number; // Number of rules for this client strategy
  created_at: string;
  updated_at: string;
}

export interface ClientStrategyRule {
  id: string;
  client_id: string;
  client_strategy_id: string;
  price_min: number;
  price_max: number;
  target: number;
  stop_loss: number;
  trailing_sl: number | null;
  quantity: number;
  variety?: 'NORMAL' | 'ROBO'; // Not in schema but might be used in UI
  mode: 'percent' | 'points';
  expiry_type: 'CE' | 'PE' | 'FUT' | 'EQ' | 'OPT';
  created_at: string;
  updated_at: string;
}

// Order Types
export interface Order {
  id: string;
  clientId: string;
  clientName: string;
  strategyId: string;
  strategyName: string;
  symbol: string;
  variety: string;
  expiryDate: string;
  expiryType: string;
  expiry_type: string; // Alternative field name
  expirydate: string; // Alternative field name
  strike: string;
  price: string;
  qty: number;
  quantity?: number; // Alternative field name
  type: 'BUY' | 'SELL';
  order_type?: 'BUY' | 'SELL'; // Alternative field name
  status: 'COMPLETE' | 'PENDING' | 'FAILED';
  created: string;
  created_at?: string; // Alternative field name
  target_price?: string;
  sl_price?: string;
  broker_order_id?: string; // Renamed from angleone_order_id
}

// Position Types
export interface Position {
  id: string;
  clientId: string;
  clientName: string;
  symbol: string;
  type: 'BUY' | 'SELL';
  quantity: number;
  entryPrice: number;
  currentPrice: number;
  pnl: number;
  created: string;
}

// Broker Audit Trail Types (AngleOne) - Updated
export interface BrokerAuditTrail {
  timestamp: string;
  audit_id: string;
  client_id: string;
  client_name: string;
  strategy_id: string;
  strategy_name: string;
  broker: string; // Added broker field
  broker_request: string; // Updated from angleone_request
  broker_response: string; // Updated from angleone_response
  status: string;
  error_message: string | null;
}

// API Signal Audit Trail Types
export interface SignalAuditTrail {
  timestamp: string;
  audit_id: string;
  request_payload: string;
  strategy_valid: number; // UInt8 in ClickHouse
  eligible_clients: string;
  strategy_name: string;
  symbol: string;
  side: string;
  quantity: number;
  price: number;
  expirydate: string;
  expiry_type: string;
  strike: number;
  status: string;
  reason: string;
  error_message: string | null;
}

// Legacy Audit Trail type for backward compatibility
export interface AuditTrail {
  id: string;
  timestamp: string;
  clientId: string;
  clientName: string;
  strategyId: string;
  strategyName: string;
  strike?: string | number;
  expiry_type?: string;
  expirydate?: string;
  request: string;
  response: string;
  status: 'SUCCESS' | 'FAILED';
  error: string | null;
}

// Dashboard Types
export interface DashboardStats {
  totalClients: number;
  totalStrategies: number;
  totalOrders: number;
  clientPnlDistribution: Array<{
    client: string;
    pnl: number;
  }>;
}

// API Response Types for Flask Server - Updated to match Flask server response format
export interface ApiResponse<T> {
  status: 'success' | 'error';
  data?: T;
  message?: string;
}

// For compatibility with existing code
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
