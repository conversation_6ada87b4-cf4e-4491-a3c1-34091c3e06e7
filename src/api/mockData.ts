import { 
  Client, 
  Strategy, 
  Order, 
  AuditTrail, 
  DashboardStats, 
  Position, 
  BrokerAuditTrail, 
  SignalAuditTrail,
  ClientStrategy,
  ClientStrategyRule
} from './types';

// Mock data for clients
export const mockClients: Client[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    broker: 'flattrade',
    api_key: 'mock_api_key_1',
    api_secret: 'mock_api_secret_1',
    user_id: 'K168112',
    password: 'password123',
    totp_key: 'TOTP123',
    macaddress: 'N/A',
    local_ip: 'N/A',
    public_ip: 'N/A',
    is_active: 1,
    created_at: '2025-05-15',
    updated_at: '2025-05-15'
  },
  {
    id: '2',
    name: 'FlatTrade Client 1',
    email: '<EMAIL>',
    broker: 'flattrade',
    api_key: 'mock_api_key_2',
    api_secret: 'mock_api_secret_2',
    user_id: 'FT060584',
    password: 'password123',
    totp_key: 'TOTP456',
    macaddress: 'N/A',
    local_ip: 'N/A',
    public_ip: 'N/A',
    is_active: 1,
    created_at: '2025-05-15',
    updated_at: '2025-05-15'
  },
  {
    id: '3',
    name: 'FlatTrade Client 2',
    email: '<EMAIL>',
    broker: 'flattrade',
    api_key: 'mock_api_key_3',
    api_secret: 'mock_api_secret_3',
    user_id: 'FT059402',
    password: 'password123',
    totp_key: 'TOTP789',
    macaddress: 'N/A',
    local_ip: 'N/A',
    public_ip: 'N/A',
    is_active: 1,
    created_at: '2025-05-15',
    updated_at: '2025-05-15'
  },
  {
    id: '4',
    name: 'Finvasia Client',
    email: '<EMAIL>',
    broker: 'finvasia',
    api_key: 'mock_api_key_4',
    api_secret: 'mock_api_secret_4',
    user_id: 'FZ17064',
    password: 'password123',
    totp_key: 'TOTP012',
    macaddress: 'N/A',
    local_ip: 'N/A',
    public_ip: 'N/A',
    is_active: 1,
    created_at: '2025-05-13',
    updated_at: '2025-05-13'
  },
  {
    id: '5',
    name: 'Angel One Premium',
    email: '<EMAIL>',
    broker: 'angel_one',
    api_key: 'mock_api_key_5',
    api_secret: 'mock_api_secret_5',
    user_id: 'AAAM515537',
    password: 'password123',
    totp_key: 'TOTP345',
    macaddress: 'N/A',
    local_ip: 'N/A',
    public_ip: 'N/A',
    is_active: 1,
    created_at: '2025-05-12',
    updated_at: '2025-05-12'
  },
  {
    id: '6',
    name: 'Angel One Standard',
    email: '<EMAIL>',
    broker: 'angel_one',
    api_key: 'mock_api_key_6',
    api_secret: 'mock_api_secret_6',
    user_id: 'AAAM405338',
    password: 'password123',
    totp_key: 'TOTP678',
    macaddress: 'N/A',
    local_ip: 'N/A',
    public_ip: 'N/A',
    is_active: 1,
    created_at: '2025-05-12',
    updated_at: '2025-05-12'
  },
  {
    id: '7',
    name: 'Kite Pro User',
    email: '<EMAIL>',
    broker: 'zerodha',
    api_key: 'mock_api_key_7',
    api_secret: 'mock_api_secret_7',
    user_id: 'K54600',
    password: 'password123',
    totp_key: 'TOTP901',
    macaddress: 'N/A',
    local_ip: 'N/A',
    public_ip: 'N/A',
    is_active: 1,
    created_at: '2025-05-12',
    updated_at: '2025-05-12'
  },
  {
    id: '8',
    name: 'Alpha Trader',
    email: '<EMAIL>',
    broker: 'upstox',
    api_key: 'mock_api_key_8',
    api_secret: 'mock_api_secret_8',
    user_id: 'A235896',
    password: 'password123',
    totp_key: 'TOTP234',
    macaddress: 'N/A',
    local_ip: 'N/A',
    public_ip: 'N/A',
    is_active: 1,
    created_at: '2025-05-07',
    updated_at: '2025-05-07'
  },
  {
    id: '9',
    name: 'Angel One Elite',
    email: '<EMAIL>',
    broker: 'angel_one',
    api_key: 'mock_api_key_9',
    api_secret: 'mock_api_secret_9',
    user_id: 'AAAM502866',
    password: 'password123',
    totp_key: 'TOTP567',
    macaddress: 'N/A',
    local_ip: 'N/A',
    public_ip: 'N/A',
    is_active: 1,
    created_at: '2025-05-06',
    updated_at: '2025-05-06'
  },
  {
    id: '10',
    name: 'Angel One Platinum',
    email: '<EMAIL>',
    broker: 'angel_one',
    api_key: 'mock_api_key_10',
    api_secret: 'mock_api_secret_10',
    user_id: 'AAAM705880',
    password: 'password123',
    totp_key: 'TOTP890',
    macaddress: 'N/A',
    local_ip: 'N/A',
    public_ip: 'N/A',
    is_active: 1,
    created_at: '2025-05-04',
    updated_at: '2025-05-04'
  },
  {
    id: '11',
    name: 'Beta Trader',
    email: '<EMAIL>',
    broker: 'angel_one',
    api_key: 'mock_api_key_11',
    api_secret: 'mock_api_secret_11',
    user_id: 'B58906041',
    password: 'password123',
    totp_key: 'TOTP123',
    macaddress: 'N/A',
    local_ip: 'N/A',
    public_ip: 'N/A',
    is_active: 1,
    created_at: '2025-05-02',
    updated_at: '2025-05-02'
  }
];

// Mock data for strategies
export const mockStrategies: Strategy[] = [
  {
    id: '1',
    name: 'Hunter',
    description: 'Hunter',
    parameters: '{}',
    is_active: true,
    created_at: '2025-04-25'
  },
  {
    id: '2',
    name: 'Hunter_Haircut',
    description: 'Hunter_Haircut',
    parameters: '{}',
    is_active: true,
    created_at: '2025-05-01'
  },
  {
    id: '3',
    name: 'Test',
    description: 'test',
    parameters: '{}',
    is_active: true,
    created_at: '2025-04-26'
  },
  {
    id: '4',
    name: 'Cherry Pick',
    description: 'Cherry Pick',
    parameters: '{}',
    is_active: true,
    created_at: '2025-05-04'
  }
];

// Mock data for client strategies
export const mockClientStrategies: ClientStrategy[] = [
  {
    client_id: '1',
    strategy_id: '1',
    clientName: 'Ritesh Mumbai', // For UI display only
    strategyName: 'Hunter', // For UI display only
    variety: 'NORMAL',
    is_enabled: true,
    preference_type: 'AS_RECEIVED',
    preferred_instrument: 'ANY',
    created_at: '2025-04-25',
    updated_at: '2025-04-25'
  },
  {
    client_id: '2',
    strategy_id: '2',
    clientName: 'Kiran L Pune', // For UI display only
    strategyName: 'Hunter_Haircut', // For UI display only
    variety: 'ROBO',
    is_enabled: false,
    preference_type: 'FORCE_BUY',
    preferred_instrument: 'CE',
    created_at: '2025-04-26',
    updated_at: '2025-04-26'
  },
  {
    client_id: '3',
    strategy_id: '4',
    clientName: 'Giridhar Batny Bangalore', // For UI display only
    strategyName: 'Cherry Pick', // For UI display only
    variety: 'NORMAL',
    is_enabled: true,
    preference_type: 'FORCE_SELL',
    preferred_instrument: 'PE',
    created_at: '2025-05-04',
    updated_at: '2025-05-04'
  }
];

// Mock data for client strategy rules
export const mockClientStrategyRules: ClientStrategyRule[] = [
  {
    id: '1',
    client_id: '1',
    client_strategy_id: '1',
    price_min: 100,
    price_max: 200,
    target: 5,
    stop_loss: 3,
    trailing_sl: 1.5,
    quantity: 10,
    mode: 'percent',
    expiry_type: 'CE',
    created_at: '2025-04-25',
    updated_at: '2025-04-25'
  },
  {
    id: '2',
    client_id: '1',
    client_strategy_id: '1',
    price_min: 201,
    price_max: 300,
    target: 7,
    stop_loss: 4,
    trailing_sl: 2,
    quantity: 15,
    mode: 'percent',
    expiry_type: 'PE',
    created_at: '2025-04-25',
    updated_at: '2025-04-25'
  },
  {
    id: '3',
    client_id: '3',
    client_strategy_id: '3',
    price_min: 500,
    price_max: 1000,
    target: 10,
    stop_loss: 5,
    trailing_sl: null,
    quantity: 5,
    mode: 'points',
    expiry_type: 'FUT',
    created_at: '2025-05-04',
    updated_at: '2025-05-04'
  }
];

// Mock data for orders
export const mockOrders: Order[] = [
  {
    id: '1',
    clientId: '9',
    clientName: 'Manoj H Pune',
    strategyId: '1',
    strategyName: 'Hunter',
    symbol: 'NIFTY15MAY2325100CE',
    variety: 'ROBO',
    expiryDate: '2025-05-15',
    expiryType: 'CE',
    strike: '25100',
    price: '₹59.30',
    qty: 75,
    type: 'BUY',
    status: 'COMPLETE',
    created: '2025-05-15 14:12:31'
  },
  {
    id: '2',
    clientId: '4',
    clientName: 'Paresh Surat',
    strategyId: '1',
    strategyName: 'Hunter',
    symbol: 'NIFTY15MAY2325100CE',
    variety: 'ROBO',
    expiryDate: '2025-05-15',
    expiryType: 'CE',
    strike: '25100',
    price: '₹59.30',
    qty: 75,
    type: 'BUY',
    status: 'COMPLETE',
    created: '2025-05-15 14:12:31'
  },
  {
    id: '3',
    clientId: '8',
    clientName: 'Kapil Ludhiana',
    strategyId: '1',
    strategyName: 'Hunter',
    symbol: 'NIFTY15MAY2325100CE',
    variety: 'ROBO',
    expiryDate: '2025-05-15',
    expiryType: 'CE',
    strike: '25100',
    price: '₹59.30',
    qty: 75,
    type: 'BUY',
    status: 'COMPLETE',
    created: '2025-05-15 14:12:31'
  },
  {
    id: '4',
    clientId: '7',
    clientName: 'Esha Ratnagiri',
    strategyId: '1',
    strategyName: 'Hunter',
    symbol: 'NIFTY15MAY2325100CE',
    variety: 'ROBO',
    expiryDate: '2025-05-15',
    expiryType: 'CE',
    strike: '25100',
    price: '₹59.30',
    qty: 75,
    type: 'BUY',
    status: 'COMPLETE',
    created: '2025-05-15 14:12:31'
  }
];

// Mock data for positions
export const mockPositions: Position[] = [];

// Mock data for audit trail
export const mockAuditTrail: AuditTrail[] = [
  {
    id: 'ab640b07-99ca-4088-b424-dade5f19cfaa',
    timestamp: 'Thu, 15 May 2025 14:12:31 GMT',
    clientId: '9',
    clientName: 'Manoj H Pune',
    strategyId: '1',
    strategyName: 'Hunter',
    request: '{"user_id":"AAAM169201","exchange":"NFO"...}',
    response: '"567a4555-dbec-4ef0-8eaf-e496d1514028"',
    status: 'SUCCESS',
    error: null
  },
  {
    id: '08aafbb5-617b-4510-99ae-11ffeabb6138',
    timestamp: 'Thu, 15 May 2025 14:12:31 GMT',
    clientId: '7',
    clientName: 'Esha Ratnagiri',
    strategyId: '1',
    strategyName: 'Hunter',
    request: '{"user_id":"AAAB002525","exchange":"NFO"...}',
    response: '"dce3c65e-21b9-41ed-8c34-83b18759acbb"',
    status: 'SUCCESS',
    error: null
  },
  {
    id: '4bde8500-2353-4fc1-8d5d-71c9d436b6c2',
    timestamp: 'Thu, 15 May 2025 14:12:31 GMT',
    clientId: '4',
    clientName: 'Paresh Surat',
    strategyId: '1',
    strategyName: 'Hunter',
    request: '{"user_id":"AAAM502866","exchange":"NFO"...}',
    response: '"a38f5b55-53eb-43aa-a966-3146052453"b"',
    status: 'SUCCESS',
    error: null
  }
];

// Mock data for broker audit trail (AngleOne) - Updated field names to match the schema
export const mockBrokerAuditTrail: BrokerAuditTrail[] = [
  {
    timestamp: '2023-08-15T10:35:27',
    audit_id: '1e9f8a3b-6d4c-45e7-9f8a-3b6d4c5e7f9a',
    client_id: 'client123',
    client_name: 'ABC Investments',
    strategy_id: 'strategy456',
    strategy_name: 'NIFTY Momentum',
    broker: 'angel_one',
    broker_request: JSON.stringify({
      symbol: 'NIFTY',
      quantity: 50,
      price: 19750.25,
      orderType: 'MARKET',
      product: 'NRML',
      validity: 'DAY',
      variety: 'NORMAL',
      tag: 'API'
    }),
    broker_response: JSON.stringify({
      status: 'success',
      orderId: 'ORDER123456',
      message: 'Order placed successfully'
    }),
    status: 'SUCCESS',
    error_message: null
  },
  {
    timestamp: '2023-08-15T11:42:13',
    audit_id: '2f8b9c4d-7e5f-56g8-9c4d-7e5f6g8h9i0j',
    client_id: 'client789',
    client_name: 'XYZ Capital',
    strategy_id: 'strategy123',
    strategy_name: 'Bank NIFTY Options',
    broker: 'zerodha',
    broker_request: JSON.stringify({
      symbol: 'BANKNIFTY',
      quantity: 25,
      price: 42150.75,
      orderType: 'LIMIT',
      product: 'MIS',
      validity: 'DAY',
      variety: 'NORMAL',
      tag: 'API'
    }),
    broker_response: JSON.stringify({
      status: 'error',
      message: 'Insufficient balance'
    }),
    status: 'FAILED',
    error_message: 'Insufficient balance in trading account'
  },
  {
    timestamp: '2023-08-16T09:15:05',
    audit_id: '3g9c0d5e-8f6g-67h9-0d5e-8f6g7h9i0j1',
    client_id: 'client456',
    client_name: 'PQR Securities',
    strategy_id: 'strategy789',
    strategy_name: 'Intraday Scalper',
    broker: 'fyers',
    broker_request: JSON.stringify({
      symbol: 'RELIANCE',
      quantity: 100,
      price: 2567.50,
      orderType: 'LIMIT',
      product: 'MIS',
      validity: 'DAY',
      variety: 'NORMAL',
      tag: 'API'
    }),
    broker_response: JSON.stringify({
      status: 'success',
      orderId: 'ORDER789012',
      message: 'Order placed successfully'
    }),
    status: 'SUCCESS',
    error_message: null
  }
];

// Mock data for API signal audit trail - Updated field names to match the schema
export const mockSignalAuditTrail: SignalAuditTrail[] = [
  {
    timestamp: '2023-08-15T10:30:15',
    audit_id: '6j2f3g8h-1i9j-90k2-3g8h-1i9j0k2l3m4',
    request_payload: JSON.stringify({
      strategyId: 'strategy456',
      symbol: 'NIFTY',
      side: 'BUY',
      quantity: 50,
      price: 19750.25,
      expiryDate: '2023-08-31',
      expiryType: 'FUT',
      strike: 0
    }),
    strategy_valid: 1,
    eligible_clients: JSON.stringify(['ABC Investments', 'XYZ Capital', 'PQR Securities']),
    strategy_name: 'NIFTY Momentum',
    symbol: 'NIFTY',
    side: 'BUY',
    quantity: 50,
    price: 19750.25,
    expirydate: '2023-08-31',
    expiry_type: 'FUT',
    strike: 0,
    status: 'SUCCESS',
    reason: 'Signal successfully processed for all eligible clients',
    error_message: null
  },
  {
    timestamp: '2023-08-15T11:40:22',
    audit_id: '7k3g4h9i-2j0k-01l3-4h9i-2j0k1l3m4n5',
    request_payload: JSON.stringify({
      strategyId: 'strategy123',
      symbol: 'BANKNIFTY',
      side: 'BUY',
      quantity: 25,
      price: 42150.75,
      expiryDate: '2023-08-31',
      expiryType: 'CE',
      strike: 42000
    }),
    strategy_valid: 1,
    eligible_clients: JSON.stringify(['XYZ Capital']),
    strategy_name: 'Bank NIFTY Options',
    symbol: 'BANKNIFTY',
    side: 'BUY',
    quantity: 25,
    price: 42150.75,
    expirydate: '2023-08-31',
    expiry_type: 'CE',
    strike: 42000,
    status: 'FAILED',
    reason: '',
    error_message: 'Client XYZ Capital has insufficient balance'
  },
  {
    timestamp: '2023-08-16T09:10:33',
    audit_id: '8l4h5i0j-3k1l-12m4-5i0j-3k1l2m4n5o6',
    request_payload: JSON.stringify({
      strategyId: 'strategy789',
      symbol: 'RELIANCE',
      side: 'BUY',
      quantity: 100,
      price: 2567.50,
      expiryDate: '',
      expiryType: 'EQ',
      strike: 0
    }),
    strategy_valid: 1,
    eligible_clients: JSON.stringify(['PQR Securities']),
    strategy_name: 'Intraday Scalper',
    symbol: 'RELIANCE',
    side: 'BUY',
    quantity: 100,
    price: 2567.50,
    expirydate: '',
    expiry_type: 'EQ',
    strike: 0,
    status: 'SUCCESS',
    reason: 'Signal successfully processed',
    error_message: null
  }
];

// Mock data for dashboard stats
export const mockDashboardStats: DashboardStats = {
  totalClients: 11,
  totalStrategies: 4,
  totalOrders: 521,
  clientPnlDistribution: [
    { client: 'Ritesh Mumbai', pnl: 0.9 },
    { client: 'Kiran L Pune', pnl: 0.8 },
    { client: 'Giridhar Batny Bangalore', pnl: 0.7 },
    { client: 'Paresh Patel Surat', pnl: 0.5 },
    { client: 'Sunny Chablani Ahmedabad', pnl: 0.6 },
    { client: 'Ashwin Kerala', pnl: 0.4 },
    { client: 'Esha Ratnagiri', pnl: 0.3 },
    { client: 'Kapil Sivariya Ludhiana', pnl: 0.2 },
    { client: 'Manoj H Pune', pnl: 0.7 },
    { client: 'Samar Mumbai', pnl: 0.5 },
    { client: 'Bharat B Goa', pnl: 0.1 }
  ]
};

// Helper function to simulate API delay
export const fetchMockData = <T>(mockData: T[]): Promise<T[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([...mockData]);
    }, 500);
  });
};
