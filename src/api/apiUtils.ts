
import { ApiConfig } from './config';
import { ApiResponse } from './types';

/**
 * Makes a fetch request to the Flask API server
 */
export async function fetchApi<T>(
  endpoint: string, 
  options?: RequestInit
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(`${ApiConfig.apiBaseUrl}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'An error occurred');
    }

    const rawData = await response.json();

    // Check if the response is already in ApiResponse format
    if (rawData.status && (rawData.status === 'success' || rawData.status === 'error')) {
      return rawData as ApiResponse<T>;
    }

    // If not, wrap the raw data in ApiResponse format
    return {
      status: 'success',
      data: rawData as T
    } as ApiResponse<T>;
  } catch (error) {
    console.error('API request failed:', error);
    return {
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Helper for GET requests
 */
export async function apiGet<T>(endpoint: string): Promise<ApiResponse<T>> {
  return fetchApi<T>(endpoint);
}

/**
 * Helper for POST requests
 */
export async function apiPost<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
  return fetchApi<T>(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

/**
 * Helper for PUT requests
 */
export async function apiPut<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
  return fetchApi<T>(endpoint, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

/**
 * Helper for DELETE requests
 */
export async function apiDelete<T>(endpoint: string): Promise<ApiResponse<T>> {
  return fetchApi<T>(endpoint, {
    method: 'DELETE',
  });
}
