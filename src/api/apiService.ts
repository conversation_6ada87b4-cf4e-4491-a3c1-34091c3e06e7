import { mockClients, mockStrategies, mockOrders, mockAuditTrail, mockDashboardStats, mockPositions, fetchMockData, mockBrokerAuditTrail, mockSignalAuditTrail } from './mockData';
import { ApiResponse, PaginatedResponse, Client, Strategy, Order, AuditTrail, DashboardStats, Position, BrokerAuditTrail, SignalAuditTrail, ClientStrategy, ClientStrategyRule } from './types';
import { ApiConfig } from './config';
import { apiGet, apiPost, apiPut, apiDelete } from './apiUtils';

// Client API Functions - Updated to match OpenAPI spec
export const getClients = async (params?: {
  page?: number;
  limit?: number;
  is_active?: boolean;
  broker?: string;
}): Promise<ApiResponse<PaginatedResponse<Client>>> => {
  if (ApiConfig.useMockData) {
    const data = await fetchMockData(mockClients);
    return {
      status: 'success',
      data: {
        data,
        total: data.length,
        page: 1,
        limit: 50,
        totalPages: 1
      }
    };
  } else {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.is_active !== undefined) queryParams.append('is_active', params.is_active.toString());
      if (params?.broker) queryParams.append('broker', params.broker);
      
      const endpoint = `/api/v1/clients/${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await apiGet<Client[]>(endpoint);
      
      if (response.status === 'success' && response.data) {
        return {
          status: 'success',
          data: {
            data: response.data,
            total: response.data.length,
            page: params?.page || 1,
            limit: params?.limit || 100,
            totalPages: Math.ceil(response.data.length / (params?.limit || 100))
          }
        };
      } else {
        return {
          status: 'error',
          message: response.message || 'Failed to get clients',
          data: {
            data: [],
            total: 0,
            page: 1,
            limit: 50,
            totalPages: 0
          }
        };
      }
    } catch (error) {
      console.error('Error getting clients:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {
          data: [],
          total: 0,
          page: 1,
          limit: 50,
          totalPages: 0
        }
      };
    }
  }
};

export const getClient = async (id: string): Promise<ApiResponse<Client>> => {
  if (ApiConfig.useMockData) {
    const client = mockClients.find(c => c.id === id);
    if (!client) {
      return { status: 'error', message: 'Client not found', data: {} as Client };
    }
    return { status: 'success', data: client };
  } else {
    try {
      return await apiGet<Client>(`/api/v1/clients/${id}`);
    } catch (error) {
      console.error('Error getting client:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: {} as Client 
      };
    }
  }
};

export const createClient = async (client: {
  name: string;
  email?: string;
  broker: string;
  is_active?: boolean;
  api_key: string;
  api_secret: string;
  user_id: string;
  password?: string;
  totp_key?: string;
  macaddress?: string;
  local_ip?: string;
  public_ip?: string;
}): Promise<ApiResponse<Client>> => {
  if (ApiConfig.useMockData) {
    const newClient = {
      ...client,
      id: Math.random().toString(36).substring(7),
      created_at: new Date().toISOString().split('T')[0],
      updated_at: new Date().toISOString().split('T')[0],
      is_active: client.is_active ? 1 : 0,
    };
    return { status: 'success', data: newClient as Client };
  } else {
    try {
      return await apiPost<Client>('/api/v1/clients/', client);
    } catch (error) {
      console.error('Error creating client:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: {} as Client 
      };
    }
  }
};

export const updateClient = async (id: string, client: {
  name?: string;
  email?: string;
  broker?: string;
  is_active?: boolean;
  api_key?: string;
  api_secret?: string;
  password?: string;
  totp_key?: string;
  macaddress?: string;
  local_ip?: string;
  public_ip?: string;
}): Promise<ApiResponse<Client>> => {
  if (ApiConfig.useMockData) {
    const existingClient = mockClients.find(c => c.id === id);
    if (!existingClient) {
      return { status: 'error', message: 'Client not found', data: {} as Client };
    }
    const updatedClient = { 
      ...existingClient, 
      ...client,
      is_active: client.is_active !== undefined ? (client.is_active ? 1 : 0) : existingClient.is_active,
      updated_at: new Date().toISOString().split('T')[0]
    };
    return { status: 'success', data: updatedClient };
  } else {
    try {
      return await apiPut<Client>(`/api/v1/clients/${id}`, client);
    } catch (error) {
      console.error('Error updating client:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: {} as Client 
      };
    }
  }
};

export const deleteClient = async (id: string): Promise<ApiResponse<boolean>> => {
  if (ApiConfig.useMockData) {
    return { status: 'success', data: true };
  } else {
    try {
      const response = await apiDelete<boolean>(`/api/v1/clients/${id}`);
      return response;
    } catch (error) {
      console.error('Error deleting client:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: false 
      };
    }
  }
};

export const toggleClient = async (id: string): Promise<ApiResponse<Client>> => {
  if (ApiConfig.useMockData) {
    const existingClient = mockClients.find(c => c.id === id);
    if (!existingClient) {
      return { status: 'error', message: 'Client not found', data: {} as Client };
    }
    const updatedClient = { 
      ...existingClient, 
      is_active: existingClient.is_active === 1 ? 0 : 1,
      updated_at: new Date().toISOString().split('T')[0]
    };
    return { status: 'success', data: updatedClient };
  } else {
    try {
      return await apiPut<Client>(`/api/v1/clients/${id}/toggle`, {});
    } catch (error) {
      console.error('Error toggling client:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: {} as Client 
      };
    }
  }
};

// Strategy API Functions
export const getStrategies = async (params?: {
  page?: number;
  limit?: number;
  is_active?: boolean;
}): Promise<ApiResponse<PaginatedResponse<Strategy>>> => {
  if (ApiConfig.useMockData) {
    const data = await fetchMockData(mockStrategies);
    return {
      status: 'success',
      data: {
        data,
        total: data.length,
        page: 1,
        limit: 50,
        totalPages: 1
      }
    };
  } else {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.is_active !== undefined) queryParams.append('is_active', params.is_active.toString());
      
      const endpoint = `/api/v1/strategies/${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await apiGet<Strategy[]>(endpoint);
      
      if (response.status === 'success' && response.data) {
        return {
          status: 'success',
          data: {
            data: response.data,
            total: response.data.length,
            page: params?.page || 1,
            limit: params?.limit || 50,
            totalPages: Math.ceil(response.data.length / (params?.limit || 50))
          }
        };
      } else {
        return {
          status: 'error',
          message: response.message || 'Failed to get strategies',
          data: {
            data: [],
            total: 0,
            page: 1,
            limit: 50,
            totalPages: 0
          }
        };
      }
    } catch (error) {
      console.error('Error getting strategies:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {
          data: [],
          total: 0,
          page: 1,
          limit: 50,
          totalPages: 0
        }
      };
    }
  }
};

// Strategy API Functions
export const getStrategy = async (id: string): Promise<ApiResponse<Strategy>> => {
  if (ApiConfig.useMockData) {
    const strategy = mockStrategies.find(s => s.id === id);
    if (!strategy) {
      return { status: 'error', message: 'Strategy not found', data: {} as Strategy };
    }
    return { status: 'success', data: strategy };
  } else {
    try {
      return await apiGet<Strategy>(`/api/v1/strategies/${id}`);
    } catch (error) {
      console.error('Error getting strategy:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: {} as Strategy 
      };
    }
  }
};

export const createStrategy = async (strategy: Omit<Strategy, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Strategy>> => {
  if (ApiConfig.useMockData) {
    const newStrategy = {
      ...strategy,
      id: Math.random().toString(36).substring(7),
      created_at: new Date().toISOString().split('T')[0],
      updated_at: new Date().toISOString().split('T')[0]
    };
    return { status: 'success', data: newStrategy as Strategy };
  } else {
    try {
      return await apiPost<Strategy>('/api/v1/strategies/', strategy);
    } catch (error) {
      console.error('Error creating strategy:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: {} as Strategy 
      };
    }
  }
};

export const updateStrategy = async (id: string, strategy: Partial<Strategy>): Promise<ApiResponse<Strategy>> => {
  if (ApiConfig.useMockData) {
    const existingStrategy = mockStrategies.find(s => s.id === id);
    if (!existingStrategy) {
      return { status: 'error', message: 'Strategy not found', data: {} as Strategy };
    }
    const updatedStrategy = { 
      ...existingStrategy, 
      ...strategy,
      updated_at: new Date().toISOString().split('T')[0]
    };
    return { status: 'success', data: updatedStrategy };
  } else {
    try {
      return await apiPut<Strategy>(`/api/v1/strategies/${id}`, strategy);
    } catch (error) {
      console.error('Error updating strategy:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: {} as Strategy 
      };
    }
  }
};

export const deleteStrategy = async (id: string): Promise<ApiResponse<boolean>> => {
  if (ApiConfig.useMockData) {
    return { status: 'success', data: true };
  } else {
    try {
      const response = await apiDelete<boolean>(`/api/v1/strategies/${id}`);
      return response;
    } catch (error) {
      console.error('Error deleting strategy:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: false 
      };
    }
  }
};

// Orders API Functions
export const getOrders = async (params?: {
  page?: number;
  limit?: number;
  client_id?: string;
  strategy_id?: string;
  status?: string;
  type?: string;
  from_date?: string;
  to_date?: string;
}): Promise<ApiResponse<PaginatedResponse<Order>>> => {
  if (ApiConfig.useMockData) {
    const data = await fetchMockData(mockOrders);
    let filteredData = [...data];
    
    if (params?.client_id) {
      filteredData = filteredData.filter(order => order.clientId === params.client_id);
    }
    if (params?.strategy_id) {
      filteredData = filteredData.filter(order => order.strategyId === params.strategy_id);
    }
    if (params?.status) {
      filteredData = filteredData.filter(order => order.status === params.status);
    }
    if (params?.type) {
      filteredData = filteredData.filter(order => order.type === params.type);
    }
    
    return {
      status: 'success',
      data: {
        data: filteredData,
        total: filteredData.length,
        page: params?.page || 1,
        limit: params?.limit || 50,
        totalPages: Math.ceil(filteredData.length / (params?.limit || 50))
      }
    };
  } else {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.client_id) queryParams.append('client_id', params.client_id);
      if (params?.strategy_id) queryParams.append('strategy_id', params.strategy_id);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.type) queryParams.append('type', params.type);
      if (params?.from_date) queryParams.append('from_date', params.from_date);
      if (params?.to_date) queryParams.append('to_date', params.to_date);
      
      const endpoint = `/api/v1/orders/${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await apiGet<Order[]>(endpoint);
      
      if (response.status === 'success' && response.data) {
        // Convert snake_case to camelCase for frontend consistency
        const formattedOrders = response.data.map((order: any) => ({
          ...order,
          clientId: order.client_id || order.clientId,
          clientName: order.client_name || order.clientName,
          strategyId: order.strategy_id || order.strategyId,
          strategyName: order.strategy_name || order.strategyName,
          expiryDate: order.expiry_date || order.expiryDate,
          expiryType: order.expiry_type || order.expiryType
        }));
        
        return {
          status: 'success',
          data: {
            data: formattedOrders,
            total: formattedOrders.length,
            page: params?.page || 1,
            limit: params?.limit || 50,
            totalPages: Math.ceil(formattedOrders.length / (params?.limit || 50))
          }
        };
      } else {
        return {
          status: 'error',
          message: response.message || 'Failed to get orders',
          data: {
            data: [],
            total: 0,
            page: 1,
            limit: 50,
            totalPages: 0
          }
        };
      }
    } catch (error) {
      console.error('Error getting orders:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {
          data: [],
          total: 0,
          page: 1,
          limit: 50,
          totalPages: 0
        }
      };
    }
  }
};

// Broker Audit Trail API Functions
export const getBrokerAuditTrail = async (params?: {
  page?: number;
  limit?: number;
  client_id?: string;
  strategy_id?: string;
  status?: string;
  from_date?: string;
  to_date?: string;
}): Promise<ApiResponse<PaginatedResponse<BrokerAuditTrail>>> => {
  if (ApiConfig.useMockData) {
    const data = await fetchMockData(mockBrokerAuditTrail);
    let filteredData = [...data];
    
    if (params?.client_id) {
      filteredData = filteredData.filter(trail => trail.client_id === params.client_id);
    }
    if (params?.strategy_id) {
      filteredData = filteredData.filter(trail => trail.strategy_id === params.strategy_id);
    }
    if (params?.status) {
      filteredData = filteredData.filter(trail => trail.status === params.status);
    }
    
    return {
      status: 'success',
      data: {
        data: filteredData,
        total: filteredData.length,
        page: params?.page || 1,
        limit: params?.limit || 50,
        totalPages: Math.ceil(filteredData.length / (params?.limit || 50))
      }
    };
  } else {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.client_id) queryParams.append('client_id', params.client_id);
      if (params?.strategy_id) queryParams.append('strategy_id', params.strategy_id);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.from_date) queryParams.append('from_date', params.from_date);
      if (params?.to_date) queryParams.append('to_date', params.to_date);
      
      const endpoint = `/api/v1/broker-audit/${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await apiGet<BrokerAuditTrail[]>(endpoint);
      
      if (response.status === 'success' && response.data) {
        return {
          status: 'success',
          data: {
            data: response.data,
            total: response.data.length,
            page: params?.page || 1,
            limit: params?.limit || 50,
            totalPages: Math.ceil(response.data.length / (params?.limit || 50))
          }
        };
      } else {
        return {
          status: 'error',
          message: response.message || 'Failed to get broker audit trail',
          data: {
            data: [],
            total: 0,
            page: 1,
            limit: 50,
            totalPages: 0
          }
        };
      }
    } catch (error) {
      console.error('Error getting broker audit trail:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {
          data: [],
          total: 0,
          page: 1,
          limit: 50,
          totalPages: 0
        }
      };
    }
  }
};

// Signal Audit Trail API Functions (renamed from Strategy Audit)
export const getStrategyAuditTrail = async (params?: {
  page?: number;
  limit?: number;
  strategy_id?: string;
  symbol?: string;
  status?: string;
  from_date?: string;
  to_date?: string;
}): Promise<ApiResponse<PaginatedResponse<SignalAuditTrail>>> => {
  if (ApiConfig.useMockData) {
    const data = await fetchMockData(mockSignalAuditTrail);
    let filteredData = [...data];
    
    if (params?.strategy_id) {
      filteredData = filteredData.filter(trail => trail.strategy_name.includes(params.strategy_id || ''));
    }
    if (params?.symbol) {
      filteredData = filteredData.filter(trail => trail.symbol === params.symbol);
    }
    if (params?.status) {
      filteredData = filteredData.filter(trail => trail.status === params.status);
    }
    
    return {
      status: 'success',
      data: {
        data: filteredData,
        total: filteredData.length,
        page: params?.page || 1,
        limit: params?.limit || 50,
        totalPages: Math.ceil(filteredData.length / (params?.limit || 50))
      }
    };
  } else {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.strategy_id) queryParams.append('strategy_id', params.strategy_id);
      if (params?.symbol) queryParams.append('symbol', params.symbol);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.from_date) queryParams.append('from_date', params.from_date);
      if (params?.to_date) queryParams.append('to_date', params.to_date);
      
      const endpoint = `/api/v1/strategy-audit/${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await apiGet<SignalAuditTrail[]>(endpoint);
      
      if (response.status === 'success' && response.data) {
        return {
          status: 'success',
          data: {
            data: response.data,
            total: response.data.length,
            page: params?.page || 1,
            limit: params?.limit || 50,
            totalPages: Math.ceil(response.data.length / (params?.limit || 50))
          }
        };
      } else {
        return {
          status: 'error',
          message: response.message || 'Failed to get strategy audit trail',
          data: {
            data: [],
            total: 0,
            page: 1,
            limit: 50,
            totalPages: 0
          }
        };
      }
    } catch (error) {
      console.error('Error getting strategy audit trail:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {
          data: [],
          total: 0,
          page: 1,
          limit: 50,
          totalPages: 0
        }
      };
    }
  }
};

// Client Strategy Rules API Functions
export const getClientStrategyRules = async (clientStrategyId: string): Promise<ApiResponse<PaginatedResponse<ClientStrategyRule>>> => {
  if (ApiConfig.useMockData) {
    // Mock data for rules
    const mockRules: ClientStrategyRule[] = [
      {
        id: 'rule1',
        client_id: 'client1',
        client_strategy_id: clientStrategyId,
        price_min: 100,
        price_max: 200,
        target: 150,
        stop_loss: 90,
        trailing_sl: 5,
        quantity: 10,
        mode: 'percent',
        expiry_type: 'CE',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }
    ];
    return {
      status: 'success',
      data: {
        data: mockRules,
        total: mockRules.length,
        page: 1,
        limit: 50,
        totalPages: 1
      }
    };
  } else {
    try {
      const response = await apiGet<ClientStrategyRule[]>(`/api/v1/client-strategy-rules/?client_strategy_id=${clientStrategyId}`);
      if (response.status === 'success' && response.data) {
        return {
          status: 'success',
          data: {
            data: response.data,
            total: response.data.length,
            page: 1,
            limit: 50,
            totalPages: 1
          }
        };
      } else {
        return {
          status: 'error',
          message: response.message || 'Failed to get client strategy rules',
          data: {
            data: [],
            total: 0,
            page: 1,
            limit: 50,
            totalPages: 0
          }
        };
      }
    } catch (error) {
      console.error('Error getting client strategy rules:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {
          data: [],
          total: 0,
          page: 1,
          limit: 50,
          totalPages: 0
        }
      };
    }
  }
};

export const createClientStrategyRule = async (rule: Omit<ClientStrategyRule, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<ClientStrategyRule>> => {
  if (ApiConfig.useMockData) {
    const newRule = {
      ...rule,
      id: Math.random().toString(36).substring(7),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    return { status: 'success', data: newRule as ClientStrategyRule };
  } else {
    try {
      return await apiPost<ClientStrategyRule>('/api/v1/client-strategy-rules/', rule);
    } catch (error) {
      console.error('Error creating client strategy rule:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: {} as ClientStrategyRule 
      };
    }
  }
};

export const updateClientStrategyRule = async (id: string, rule: Partial<ClientStrategyRule>): Promise<ApiResponse<ClientStrategyRule>> => {
  if (ApiConfig.useMockData) {
    const updatedRule = {
      id,
      ...rule,
      updated_at: new Date().toISOString()
    };
    return { status: 'success', data: updatedRule as ClientStrategyRule };
  } else {
    try {
      return await apiPut<ClientStrategyRule>(`/api/v1/client-strategy-rules/${id}`, rule);
    } catch (error) {
      console.error('Error updating client strategy rule:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: {} as ClientStrategyRule 
      };
    }
  }
};

export const deleteClientStrategyRule = async (id: string): Promise<ApiResponse<boolean>> => {
  if (ApiConfig.useMockData) {
    return { status: 'success', data: true };
  } else {
    try {
      const response = await apiDelete<boolean>(`/api/v1/client-strategy-rules/${id}`);
      return response;
    } catch (error) {
      console.error('Error deleting client strategy rule:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: false 
      };
    }
  }
};

// Client Strategy API Functions
export const getClientStrategies = async (params?: {
  page?: number;
  limit?: number;
  client_id?: string;
  strategy_id?: string;
  is_enabled?: boolean;
}): Promise<ApiResponse<PaginatedResponse<ClientStrategy>>> => {
  if (ApiConfig.useMockData) {
    // Mock data for client strategies
    const mockClientStrategies: ClientStrategy[] = [
      {
        client_id: 'client1',
        strategy_id: 'strategy1',
        clientName: 'John Doe',
        strategyName: 'Strategy A',
        variety: 'NORMAL',
        is_enabled: true,
        preference_type: 'AS_RECEIVED',
        preferred_instrument: 'ANY',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }
    ];
    return {
      status: 'success',
      data: {
        data: mockClientStrategies,
        total: mockClientStrategies.length,
        page: 1,
        limit: 50,
        totalPages: 1
      }
    };
  } else {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.client_id) queryParams.append('client_id', params.client_id);
      if (params?.strategy_id) queryParams.append('strategy_id', params.strategy_id);
      if (params?.is_enabled !== undefined) queryParams.append('is_enabled', params.is_enabled.toString());
      
      const endpoint = `/api/v1/client-strategies/${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await apiGet<ClientStrategy[]>(endpoint);
      
      if (response.status === 'success' && response.data) {
        return {
          status: 'success',
          data: {
            data: response.data,
            total: response.data.length,
            page: params?.page || 1,
            limit: params?.limit || 50,
            totalPages: Math.ceil(response.data.length / (params?.limit || 50))
          }
        };
      } else {
        return {
          status: 'error',
          message: response.message || 'Failed to get client strategies',
          data: {
            data: [],
            total: 0,
            page: 1,
            limit: 50,
            totalPages: 0
          }
        };
      }
    } catch (error) {
      console.error('Error getting client strategies:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {
          data: [],
          total: 0,
          page: 1,
          limit: 50,
          totalPages: 0
        }
      };
    }
  }
};

export const updateClientStrategy = async (clientId: string, strategyId: string, updates: Partial<ClientStrategy>): Promise<ApiResponse<ClientStrategy>> => {
  if (ApiConfig.useMockData) {
    const updatedClientStrategy = {
      client_id: clientId,
      strategy_id: strategyId,
      ...updates,
      updated_at: new Date().toISOString()
    };
    return { status: 'success', data: updatedClientStrategy as ClientStrategy };
  } else {
    try {
      return await apiPut<ClientStrategy>(`/api/v1/client-strategies/${clientId}/${strategyId}`, updates);
    } catch (error) {
      console.error('Error updating client strategy:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: {} as ClientStrategy 
      };
    }
  }
};

// Legacy functions for backward compatibility
export const getSignalAuditTrail = getStrategyAuditTrail;

// Legacy Audit Trail function (for backward compatibility)
export const getAuditTrail = async (params?: {
  page?: number;
  limit?: number;
  client_id?: string;
  strategy_id?: string;
  status?: string;
  from_date?: string;
  to_date?: string;
}): Promise<ApiResponse<PaginatedResponse<AuditTrail>>> => {
  // Convert BrokerAuditTrail to AuditTrail format for compatibility
  const response = await getBrokerAuditTrail(params);
  if (response.status === 'success') {
    const convertedData = response.data.data.map((trail: BrokerAuditTrail): AuditTrail => ({
      id: trail.audit_id,
      timestamp: trail.timestamp,
      clientId: trail.client_id,
      clientName: trail.client_name,
      strategyId: trail.strategy_id,
      strategyName: trail.strategy_name,
      request: trail.broker_request,
      response: trail.broker_response,
      status: trail.status as 'SUCCESS' | 'FAILED',
      error: trail.error_message
    }));
    
    return {
      status: 'success',
      data: {
        ...response.data,
        data: convertedData
      }
    };
  }
  return {
    status: 'error',
    message: response.message,
    data: {
      data: [],
      total: 0,
      page: 1,
      limit: 50,
      totalPages: 0
    }
  };
};

// Place Order API Function
export const placeOrder = async (orderData: {
  client_id: string;
  strategy_id: string;
  symbol: string;
  variety: string;
  expiry_date: string;
  expiry_type: string;
  strike: string;
  price: string;
  qty: number;
  type: 'BUY' | 'SELL';
  client_name: string;
  strategy_name: string;
}): Promise<ApiResponse<Order>> => {
  if (ApiConfig.useMockData) {
    const newOrder = {
      ...orderData,
      id: Math.random().toString(36).substring(7),
      status: 'PENDING',
      created: new Date().toISOString().split('T')[0],
      clientId: orderData.client_id,
      clientName: orderData.client_name,
      strategyId: orderData.strategy_id,
      strategyName: orderData.strategy_name,
      expiryDate: orderData.expiry_date,
      expiryType: orderData.expiry_type,
    };
    return { status: 'success', data: newOrder as Order };
  } else {
    try {
      return await apiPost<Order>('/api/v1/orders', orderData);
    } catch (error) {
      console.error('Error placing order:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: {} as Order 
      };
    }
  }
};

// Dashboard API Functions
export const getDashboardStats = async (): Promise<ApiResponse<DashboardStats>> => {
  if (ApiConfig.useMockData) {
    const data = await fetchMockData([mockDashboardStats]);
    return { 
      status: 'success', 
      data: data[0]
    };
  } else {
    try {
      return await apiGet<DashboardStats>('/api/v1/dashboard/stats');
    } catch (error) {
      console.error('Error getting dashboard stats:', error);
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error', 
        data: {} as DashboardStats 
      };
    }
  }
};

// Positions API Functions
export const getPositions = async (clientId?: string): Promise<ApiResponse<PaginatedResponse<Position>>> => {
  if (ApiConfig.useMockData) {
    let data = await fetchMockData(mockPositions);
    if (clientId) {
      data = data.filter(p => p.clientId === clientId);
    }
    return {
      status: 'success',
      data: {
        data,
        total: data.length,
        page: 1,
        limit: 50,
        totalPages: 1
      }
    };
  } else {
    try {
      const endpoint = clientId ? `/api/v1/positions?client_id=${clientId}` : '/api/v1/positions';
      const response = await apiGet<Position[]>(endpoint);
      if (response.status === 'success' && response.data) {
        return {
          status: 'success',
          data: {
            data: response.data,
            total: response.data.length,
            page: 1,
            limit: 50,
            totalPages: 1
          }
        };
      } else {
        return {
          status: 'error',
          message: response.message || 'Failed to get positions',
          data: {
            data: [],
            total: 0,
            page: 1,
            limit: 50,
            totalPages: 0
          }
        };
      }
    } catch (error) {
      console.error('Error getting positions:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {
          data: [],
          total: 0,
          page: 1,
          limit: 50,
          totalPages: 0
        }
      };
    }
  }
};

// Statistics API Functions - New Position APIs

// Types for Statistics API responses
export interface StatisticsPosition {
  tradingsymbol: string;
  exchange: string;
  product: string;
  qty: number;
  buy_qty: number;
  sell_qty: number;
  lot_size: number;
  ltp: number;
  buy_avg_price: number;
  buy_value: number;
  sell_avg_price: number;
  sell_value: number;
  unrealized: number;
  realized: number;
  broker: string;
}

export interface ClientPositionSummary {
  user_id: string;
  timestamp: string;
  positions: StatisticsPosition[];
  summary: {
    total_positions: number;
    total_unrealized: number;
    total_realized: number;
    brokers: string[];
  };
  errors: string[];
}

export interface AllClientsPositionsResponse {
  timestamp: string;
  clients: Record<string, ClientPositionSummary>;
  summary: {
    total_clients: number;
    total_positions: number;
    total_unrealized: number;
    brokers: string[];
  };
  errors: string[];
}

export interface PositionsSummaryResponse {
  timestamp: string;
  broker_filter: string;
  total_clients: number;
  total_positions: number;
  total_unrealized_pnl: number;
  total_realized_pnl: number;
  total_pnl: number;
  brokers_active: string[];
  clients_with_positions: number;
  clients_with_errors: number;
  position_breakdown: {
    by_broker: Record<string, { count: number; unrealized: number; realized: number }>;
    by_exchange: Record<string, { count: number; unrealized: number; realized: number }>;
    by_product: Record<string, { count: number; unrealized: number; realized: number }>;
  };
}

export interface StatisticsOrder {
  orderid: string;
  order_id: string;
  updatetime: string;
  order_timestamp: string;
  order_entry_time: string;
  tradingsymbol: string;
  ordertype: string;
  order_type: string;
  quantity: number;
  price: number;
  status: string;
  variety: string;
  producttype: string;
  duration: string;
  symboltoken: string;
  exchange: string;
  client_id?: string;
  client_name?: string;
}

// Broker Orders API Types - Standardized across brokers
export interface BrokerOrder {
  id?: string;             // Unique order ID (optional for backward compatibility)
  time: string;            // Order timestamp (DD-MM-YYYY HH:MI AM/PM format)
  order_id: string;        // Standard field mapped from orderid/norenordno
  symbol: string;          // Standard field mapped from tradingsymbol/tsym
  buy_sell: string;        // Standard field mapped from transactiontype/trantype (B/S)
  variety: string;         // Standard field mapped from variety/prd (B, BO, CO, etc.)
  price: number;           // Standard field mapped from price/prc
  qty: number;             // Standard field mapped from lotsize/qty
  target?: number;         // Standard field mapped from squareoff (AngleOne only)
  sl?: number;             // Standard field mapped from stoploss (AngleOne only)
  trailing_sl?: number;    // Standard field mapped from trailingstoploss (AngleOne only)
  status: string;          // Standard field mapped from status/stat (Ok, etc.)
  order_status: string;    // Standard field mapped from orderstatus/status (REJECTED, COMPLETE, etc.)
  reason?: string;         // Standard field mapped from text/rejreason
  broker: string;          // Broker identifier (flattrade, angleone, etc.)
  timestamp: string;       // Order timestamp (fallback field)
  exchange?: string;       // Exchange where the order was placed (e.g., NSE, BSE)
  client_id?: string;      // Client user ID
  client_name?: string;    // Client display name
}

export interface BrokerOrdersResponse {
  user_id: string;
  timestamp: string;
  orders: BrokerOrder[];
  summary: {
    total_orders: number;
    pending_orders: number;
    completed_orders: number;
    cancelled_orders: number;
    rejected_orders: number;
  };
  errors: string[];
}

export interface AllBrokerOrdersResponse {
  timestamp: string;
  clients: Record<string, BrokerOrdersResponse>;
  summary: {
    total_clients: number;
    total_orders: number;
    pending_orders: number;
    completed_orders: number;
    cancelled_orders: number;
    rejected_orders: number;
  };
  errors: string[];
}

export interface BrokerOrdersSummaryResponse {
  timestamp: string;
  broker_filter: string;
  total_clients: number;
  total_orders: number;
  pending_orders: number;
  completed_orders: number;
  cancelled_orders: number;
  rejected_orders: number;
  brokers_active: string[];
  clients_with_orders: number;
  clients_with_errors: number;
  order_breakdown: {
    by_broker: Record<string, { count: number; pending: number; completed: number }>;
    by_status: Record<string, { count: number; percentage: number }>;
    by_variety: Record<string, { count: number; percentage: number }>;
  };
}

export interface OrderActionResponse {
  status: 'success' | 'error';
  message: string;
  order_id?: string;
  data?: any;
}

export interface ClientOrdersResponse {
  user_id: string;
  timestamp: string;
  orders: StatisticsOrder[];
  summary: {
    total_orders: number;
    pending_orders: number;
    completed_orders: number;
    failed_orders: number;
  };
  errors: string[];
}

export interface AllClientsOrdersResponse {
  timestamp: string;
  clients: Record<string, ClientOrdersResponse>;
  summary: {
    total_clients: number;
    total_orders: number;
    pending_orders: number;
    completed_orders: number;
    failed_orders: number;
  };
  errors: string[];
}

/**
 * Get positions for a specific client
 * @param userId - Client user ID
 * @param broker - Optional broker filter (e.g., 'angleone', 'flattrade')
 */
export const getClientPositions = async (
  userId: string,
  broker?: string
): Promise<ApiResponse<ClientPositionSummary>> => {
  if (ApiConfig.useMockData) {
    // Mock data for client positions
    const mockClientPositions: ClientPositionSummary = {
      user_id: userId,
      timestamp: new Date().toISOString(),
      positions: [
        {
          tradingsymbol: "RELIANCE-EQ",
          exchange: "NSE",
          product: "INTRADAY",
          qty: 100,
          buy_qty: 150,
          sell_qty: 50,
          lot_size: 1,
          ltp: 2500.50,
          buy_avg_price: 2495.25,
          buy_value: 374287.50,
          sell_avg_price: 2505.75,
          sell_value: 125287.50,
          unrealized: 525.00,
          realized: 0.0,
          broker: broker || "angleone"
        }
      ],
      summary: {
        total_positions: 1,
        total_unrealized: 525.00,
        total_realized: 0.0,
        brokers: [broker || "angleone"]
      },
      errors: []
    };
    return { status: 'success', data: mockClientPositions };
  } else {
    try {
      const queryParams = new URLSearchParams();
      if (broker) queryParams.append('broker', broker);

      const endpoint = `/api/v1/positions/client/${userId}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      return await apiGet<ClientPositionSummary>(endpoint);
    } catch (error) {
      console.error('Error getting client positions:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {} as ClientPositionSummary
      };
    }
  }
};

/**
 * Get positions for all clients
 * @param broker - Optional broker filter (e.g., 'angleone', 'flattrade')
 */
export const getAllClientsPositions = async (
  broker?: string
): Promise<ApiResponse<AllClientsPositionsResponse>> => {
  if (ApiConfig.useMockData) {
    // Mock data for all clients positions
    const mockAllPositions: AllClientsPositionsResponse = {
      timestamp: new Date().toISOString(),
      clients: {
        "RITESH001": {
          user_id: "RITESH001",
          timestamp: new Date().toISOString(),
          positions: [
            {
              tradingsymbol: "RELIANCE-EQ",
              exchange: "NSE",
              product: "INTRADAY",
              qty: 100,
              buy_qty: 150,
              sell_qty: 50,
              lot_size: 1,
              ltp: 2500.50,
              buy_avg_price: 2495.25,
              buy_value: 374287.50,
              sell_avg_price: 2505.75,
              sell_value: 125287.50,
              unrealized: 525,
              realized: 0,
              broker: broker || "zerodha"
            }
          ],
          summary: {
            total_positions: 1,
            total_unrealized: 525,
            total_realized: 0,
            brokers: [broker || "zerodha"]
          },
          errors: []
        },
        "KIRAN002": {
          user_id: "KIRAN002",
          timestamp: new Date().toISOString(),
          positions: [
            {
              tradingsymbol: "TCS-EQ",
              exchange: "NSE",
              product: "DELIVERY",
              qty: 50,
              buy_qty: 50,
              sell_qty: 0,
              lot_size: 1,
              ltp: 3750.25,
              buy_avg_price: 3700.00,
              buy_value: 185000.00,
              sell_avg_price: 0,
              sell_value: 0,
              unrealized: 2512.50,
              realized: 0,
              broker: broker || "angel_one"
            }
          ],
          summary: {
            total_positions: 1,
            total_unrealized: 2512.50,
            total_realized: 0,
            brokers: [broker || "angel_one"]
          },
          errors: []
        }
      },
      summary: {
        total_clients: 2,
        total_positions: 2,
        total_unrealized: 3037.50,
        brokers: [broker || "zerodha", broker || "angel_one"]
      },
      errors: []
    };
    return { status: 'success', data: mockAllPositions };
  } else {
    try {
      const queryParams = new URLSearchParams();
      if (broker) queryParams.append('broker', broker);

      const endpoint = `/api/v1/positions/all${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      return await apiGet<AllClientsPositionsResponse>(endpoint);
    } catch (error) {
      console.error('Error getting all clients positions:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {} as AllClientsPositionsResponse
      };
    }
  }
};

/**
 * Get positions summary and analytics
 * @param broker - Optional broker filter
 */
export const getPositionsSummary = async (
  broker?: string
): Promise<ApiResponse<PositionsSummaryResponse>> => {
  if (ApiConfig.useMockData) {
    // Mock data for positions summary
    const mockSummary: PositionsSummaryResponse = {
      timestamp: new Date().toISOString(),
      broker_filter: broker || "all",
      total_clients: 5,
      total_positions: 25,
      total_unrealized_pnl: 15250.75,
      total_realized_pnl: 8750.25,
      total_pnl: 24001.00,
      brokers_active: ["angleone", "flattrade"],
      clients_with_positions: 4,
      clients_with_errors: 1,
      position_breakdown: {
        by_broker: {
          "angleone": { count: 15, unrealized: 9250.50, realized: 5250.25 },
          "flattrade": { count: 10, unrealized: 6000.25, realized: 3500.00 }
        },
        by_exchange: {
          "NSE": { count: 20, unrealized: 12500.75, realized: 7000.25 },
          "BSE": { count: 5, unrealized: 2750.00, realized: 1750.00 }
        },
        by_product: {
          "INTRADAY": { count: 18, unrealized: 11250.50, realized: 6250.25 },
          "DELIVERY": { count: 7, unrealized: 4000.25, realized: 2500.00 }
        }
      }
    };
    return { status: 'success', data: mockSummary };
  } else {
    try {
      const queryParams = new URLSearchParams();
      if (broker) queryParams.append('broker', broker);

      const endpoint = `/api/v1/positions/summary${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      return await apiGet<PositionsSummaryResponse>(endpoint);
    } catch (error) {
      console.error('Error getting positions summary:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {} as PositionsSummaryResponse
      };
    }
  }
};

/**
 * Get orders for a specific client
 * @param userId - Client user ID
 * @param broker - Optional broker filter
 */
export const getClientOrders = async (
  userId: string,
  broker?: string
): Promise<ApiResponse<ClientOrdersResponse>> => {
  if (ApiConfig.useMockData) {
    // Mock data for client orders
    const mockClientOrders: ClientOrdersResponse = {
      user_id: userId,
      timestamp: new Date().toISOString(),
      orders: [
        {
          orderid: "ORD123456",
          order_id: "ORD123456",
          updatetime: new Date().toISOString(),
          order_timestamp: new Date().toISOString(),
          order_entry_time: new Date().toISOString(),
          tradingsymbol: "RELIANCE-EQ",
          ordertype: "BUY",
          order_type: "BUY",
          quantity: 100,
          price: 2500.50,
          status: "COMPLETE",
          variety: "NORMAL",
          producttype: "INTRADAY",
          duration: "DAY",
          symboltoken: "2885",
          exchange: "NSE"
        }
      ],
      summary: {
        total_orders: 1,
        pending_orders: 0,
        completed_orders: 1,
        failed_orders: 0
      },
      errors: []
    };
    return { status: 'success', data: mockClientOrders };
  } else {
    try {
      const queryParams = new URLSearchParams();
      if (broker) queryParams.append('broker', broker);

      const endpoint = `/api/v1/orders/client/${userId}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      return await apiGet<ClientOrdersResponse>(endpoint);
    } catch (error) {
      console.error('Error getting client orders:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {} as ClientOrdersResponse
      };
    }
  }
};

/**
 * Get orders for all clients
 * @param broker - Optional broker filter
 */
export const getAllClientsOrders = async (
  broker?: string
): Promise<ApiResponse<AllClientsOrdersResponse>> => {
  if (ApiConfig.useMockData) {
    // Mock data for all clients orders
    const mockAllOrders: AllClientsOrdersResponse = {
      timestamp: new Date().toISOString(),
      clients: {
        "RITESH001": {
          user_id: "RITESH001",
          timestamp: new Date().toISOString(),
          orders: [
            {
              orderid: "ORD123456",
              order_id: "ORD123456",
              updatetime: new Date().toISOString(),
              order_timestamp: new Date().toISOString(),
              order_entry_time: new Date().toISOString(),
              tradingsymbol: "RELIANCE-EQ",
              ordertype: "BUY",
              order_type: "BUY",
              quantity: 100,
              price: 2500.50,
              status: "COMPLETE",
              variety: "NORMAL",
              producttype: "INTRADAY",
              duration: "DAY",
              symboltoken: "2885",
              exchange: "NSE"
            }
          ],
          summary: {
            total_orders: 1,
            pending_orders: 0,
            completed_orders: 1,
            failed_orders: 0
          },
          errors: []
        },
        "KIRAN002": {
          user_id: "KIRAN002",
          timestamp: new Date().toISOString(),
          orders: [
            {
              orderid: "ORD789012",
              order_id: "ORD789012",
              updatetime: new Date().toISOString(),
              order_timestamp: new Date().toISOString(),
              order_entry_time: new Date().toISOString(),
              tradingsymbol: "TCS-EQ",
              ordertype: "SELL",
              order_type: "SELL",
              quantity: 25,
              price: 3750.00,
              status: "PENDING",
              variety: "NORMAL",
              producttype: "DELIVERY",
              duration: "DAY",
              symboltoken: "11536",
              exchange: "NSE"
            }
          ],
          summary: {
            total_orders: 1,
            pending_orders: 1,
            completed_orders: 0,
            failed_orders: 0
          },
          errors: []
        }
      },
      summary: {
        total_clients: 2,
        total_orders: 2,
        pending_orders: 1,
        completed_orders: 1,
        failed_orders: 0
      },
      errors: []
    };
    return { status: 'success', data: mockAllOrders };
  } else {
    try {
      const queryParams = new URLSearchParams();
      if (broker) queryParams.append('broker', broker);

      const endpoint = `/api/v1/orders/all${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      return await apiGet<AllClientsOrdersResponse>(endpoint);
    } catch (error) {
      console.error('Error getting all clients orders:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {} as AllClientsOrdersResponse
      };
    }
  }
};

// Broker Orders API Functions - New standardized endpoints

/**
 * Get broker orders for a specific client
 * @param userId - Client user ID
 * @param broker - Optional broker filter
 */
export const getBrokerClientOrders = async (
  userId: string,
  broker?: string
): Promise<ApiResponse<BrokerOrdersResponse>> => {
  if (ApiConfig.useMockData) {
    // Mock data for broker client orders
    const mockBrokerOrders: BrokerOrdersResponse = {
      user_id: userId,
      timestamp: new Date().toISOString(),
      orders: [
        {
          order_id: "ORD123456",
          time: "05-Jun-2025 19:50:13",
          symbol: "RELIANCE-EQ",
          buy_sell: "BUY",
          variety: "NORMAL",
          price: 2500.50,
          qty: 100,
          target: 2600.00,
          sl: 2400.00,
          trailing_sl: 50.00,
          status: "COMPLETE",
          order_status: "EXECUTED",
          reason: null,
          broker: broker || "angleone",
          timestamp: new Date().toISOString(),
          client_id: userId,
          client_name: userId
        }
      ],
      summary: {
        total_orders: 1,
        pending_orders: 0,
        completed_orders: 1,
        cancelled_orders: 0,
        rejected_orders: 0
      },
      errors: []
    };
    return { status: 'success', data: mockBrokerOrders };
  } else {
    try {
      const queryParams = new URLSearchParams();
      if (broker) queryParams.append('broker', broker);

      const endpoint = `/api/v1/broker-orders/client/${userId}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      return await apiGet<BrokerOrdersResponse>(endpoint);
    } catch (error) {
      console.error('Error getting broker client orders:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {} as BrokerOrdersResponse
      };
    }
  }
};

/**
 * Get broker orders for all clients
 * @param broker - Optional broker filter
 */
export const getAllBrokerOrders = async (
  broker?: string
): Promise<ApiResponse<AllBrokerOrdersResponse>> => {
  if (ApiConfig.useMockData) {
    // Mock data for all broker orders
    const mockAllBrokerOrders: AllBrokerOrdersResponse = {
      timestamp: new Date().toISOString(),
      clients: {
        "RITESH001": {
          user_id: "RITESH001",
          timestamp: new Date().toISOString(),
          orders: [
            {
              order_id: "ORD123456",
              time: "05-Jun-2025 19:50:13",
              symbol: "RELIANCE-EQ",
              buy_sell: "BUY",
              variety: "NORMAL",
              price: 2500.50,
              qty: 100,
              target: 2600.00,
              sl: 2400.00,
              status: "COMPLETE",
              order_status: "EXECUTED",
              broker: broker || "zerodha",
              timestamp: new Date().toISOString(),
              client_id: "RITESH001",
              client_name: "RITESH001"
            }
          ],
          summary: {
            total_orders: 1,
            pending_orders: 0,
            completed_orders: 1,
            cancelled_orders: 0,
            rejected_orders: 0
          },
          errors: []
        },
        "KIRAN002": {
          user_id: "KIRAN002",
          timestamp: new Date().toISOString(),
          orders: [
            {
              order_id: "ORD789012",
              time: "05-Jun-2025 20:15:30",
              symbol: "TCS-EQ",
              buy_sell: "SELL",
              variety: "NORMAL",
              price: 3750.00,
              qty: 25,
              target: 3700.00,
              sl: 3800.00,
              status: "PENDING",
              order_status: "OPEN",
              broker: broker || "angel_one",
              timestamp: new Date().toISOString(),
              client_id: "KIRAN002",
              client_name: "KIRAN002"
            }
          ],
          summary: {
            total_orders: 1,
            pending_orders: 1,
            completed_orders: 0,
            cancelled_orders: 0,
            rejected_orders: 0
          },
          errors: []
        }
      },
      summary: {
        total_clients: 2,
        total_orders: 2,
        pending_orders: 1,
        completed_orders: 1,
        cancelled_orders: 0,
        rejected_orders: 0
      },
      errors: []
    };
    return { status: 'success', data: mockAllBrokerOrders };
  } else {
    try {
      const queryParams = new URLSearchParams();
      if (broker) queryParams.append('broker', broker);

      const endpoint = `/api/v1/broker-orders/all${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      return await apiGet<AllBrokerOrdersResponse>(endpoint);
    } catch (error) {
      console.error('Error getting all broker orders:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {} as AllBrokerOrdersResponse
      };
    }
  }
};

/**
 * Get broker orders summary and analytics
 * @param broker - Optional broker filter
 */
export const getBrokerOrdersSummary = async (
  broker?: string
): Promise<ApiResponse<BrokerOrdersSummaryResponse>> => {
  if (ApiConfig.useMockData) {
    // Mock data for broker orders summary
    const mockSummary: BrokerOrdersSummaryResponse = {
      timestamp: new Date().toISOString(),
      broker_filter: broker || "all",
      total_clients: 5,
      total_orders: 25,
      pending_orders: 5,
      completed_orders: 18,
      cancelled_orders: 1,
      rejected_orders: 1,
      brokers_active: ["angleone", "flattrade"],
      clients_with_orders: 4,
      clients_with_errors: 1,
      order_breakdown: {
        by_broker: {
          "angleone": { count: 15, pending: 3, completed: 11 },
          "flattrade": { count: 10, pending: 2, completed: 7 }
        },
        by_status: {
          "COMPLETE": { count: 18, percentage: 72 },
          "PENDING": { count: 5, percentage: 20 },
          "CANCELLED": { count: 1, percentage: 4 },
          "REJECTED": { count: 1, percentage: 4 }
        },
        by_variety: {
          "NORMAL": { count: 20, percentage: 80 },
          "ROBO": { count: 5, percentage: 20 }
        }
      }
    };
    return { status: 'success', data: mockSummary };
  } else {
    try {
      const queryParams = new URLSearchParams();
      if (broker) queryParams.append('broker', broker);

      const endpoint = `/api/v1/broker-orders/summary${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      return await apiGet<BrokerOrdersSummaryResponse>(endpoint);
    } catch (error) {
      console.error('Error getting broker orders summary:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {} as BrokerOrdersSummaryResponse
      };
    }
  }
};

/**
 * Cancel an order
 * @param userId - Client user ID
 * @param orderId - Order ID to cancel
 * @param broker - Broker identifier
 */
export const cancelOrder = async (
  userId: string,
  orderId: string,
  broker: string,
  tradingsymbol: string,
  exchange: string
): Promise<ApiResponse<OrderActionResponse>> => {
  if (ApiConfig.useMockData) {
    // Mock response for cancel order
    const mockResponse: OrderActionResponse = {
      status: 'success',
      message: 'Order cancelled successfully',
      order_id: orderId
    };
    return { status: 'success', data: mockResponse };
  } else {
    try {
      const payload = {
        user_id: userId,
        order_id: orderId,
        broker: broker
      };

      const endpoint = `/api/v1/broker-orders/cancel`;
      return await apiPost<OrderActionResponse>(endpoint, payload);
    } catch (error) {
      console.error('Error cancelling order:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error'
        } as OrderActionResponse
      };
    }
  }
};

/**
 * Modify an order
 * @param userId - Client user ID
 * @param orderId - Order ID to modify
 * @param broker - Broker identifier
 * @param modifications - Order modifications
 */
export const modifyOrder = async (
  userId: string,
  orderId: string,
  broker: string,
  modifications: {
    price?: number;
    qty?: number;
    target?: number;
    sl?: number;
    trailing_sl?: number;
    tradingsymbol?: string;
    exchange?: string;
  }
): Promise<ApiResponse<OrderActionResponse>> => {
  if (ApiConfig.useMockData) {
    // Mock response for modify order
    const mockResponse: OrderActionResponse = {
      status: 'success',
      message: 'Order modified successfully',
      order_id: orderId,
      data: modifications
    };
    return { status: 'success', data: mockResponse };
  } else {
    try {
      // Create a copy of modifications to avoid mutating the original object
      const modificationsCopy = { ...modifications };
      
      // Ensure tradingsymbol and exchange are included in the payload
      const payload = {
        user_id: userId,
        order_id: orderId,
        broker,
        tradingsymbol: modificationsCopy.tradingsymbol || '',
        exchange: modificationsCopy.exchange || 'NSE', // Default to NSE if not provided
        ...modificationsCopy
      };

      const endpoint = `/api/v1/broker-orders/modify`;
      return await apiPost<OrderActionResponse>(endpoint, payload);
    } catch (error) {
      console.error('Error modifying order:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        data: {
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error'
        } as OrderActionResponse
      };
    }
  }
};
