
// API Configuration

// Create store for API configuration
export const ApiConfig = {
  // Flag to toggle between mock and live API
  useMockData: false,

  // Base URL for the Flask API server
  //apiBaseUrl: 'http://localhost:8000',
  apiBaseUrl: 'http://*************:8000',

  // Toggle function to switch between mock and live data
  toggleMockData: () => {
    ApiConfig.useMockData = !ApiConfig.useMockData;
    return ApiConfig.useMockData;
  }
};
