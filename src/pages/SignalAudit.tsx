
import React, { useState, useEffect } from 'react';
import { useToast } from "@/hooks/use-toast";
import { getStrategyAuditTrail } from '@/api/apiService';
import { SignalAuditTrail } from '@/api/types';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RefreshCw, Filter, Target, Activity, ChevronUp, ChevronDown, Clock, Hash, TrendingUp, IndianRupee, Package, Calendar, CheckCircle, Users, AlertCircle } from 'lucide-react';
import DataTableFilter from '@/components/Filters/DataTableFilter';
import { ApiConfig } from '@/api/config';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import EnhancedJsonViewer from '@/components/EnhancedJsonViewer';
import { getStatusChipConfig } from '@/utils/orderTypeUtils';

// Utility function to format timestamp (DD-MM-YYYY HH:MI AM/PM)
const formatTimestamp = (timestamp: string): string => {
  try {
    const date = new Date(timestamp);
    return date.toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).replace(',', '');
  } catch (e) {
    // If parsing fails, try to remove timezone info manually
    return timestamp.replace(/\s*GMT.*$/, '').replace(/\s*\+.*$/, '').replace(/T.*Z$/, '');
  }
};

// Utility function to parse and format eligible clients
const formatEligibleClients = (eligibleClientsData: string): { names: string[], count: number } => {
  try {
    let clientsArray: any[] = [];

    // Try to parse the JSON data
    const parsed = JSON.parse(eligibleClientsData);

    if (Array.isArray(parsed)) {
      clientsArray = parsed;
    } else if (parsed && typeof parsed === 'object') {
      // If it's an object, try to extract client names from various possible structures
      if (parsed.clients && Array.isArray(parsed.clients)) {
        clientsArray = parsed.clients;
      } else if (parsed.eligible_clients && Array.isArray(parsed.eligible_clients)) {
        clientsArray = parsed.eligible_clients;
      } else {
        // If it's a single client object, wrap it in an array
        clientsArray = [parsed];
      }
    }

    // Extract client names, clean them, and sort them
    const clientNames = clientsArray
      .map(client => {
        if (typeof client === 'string') return client;
        if (client && typeof client === 'object') {
          return client.name || client.client_name || client.clientName || String(client);
        }
        return String(client);
      })
      .filter(name => name && name.trim() !== '')
      .map(name => {
        // Clean up the name by removing quotes, brackets, and extra whitespace
        return name
          .replace(/^['"\[\]]+|['"\[\]]+$/g, '') // Remove quotes and brackets from start/end
          .replace(/['"\[\]]/g, '') // Remove any remaining quotes and brackets
          .trim();
      })
      .filter(name => name !== '') // Filter out empty names after cleaning
      .sort();

    return { names: clientNames, count: clientNames.length };
  } catch (e) {
    // If parsing fails, try to extract names from string
    let stringData = String(eligibleClientsData);

    // Clean up the string data
    stringData = stringData
      .replace(/^['"\[\]]+|['"\[\]]+$/g, '') // Remove quotes and brackets from start/end
      .replace(/['"\[\]]/g, '') // Remove any remaining quotes and brackets
      .trim();

    if (stringData.includes(',')) {
      const names = stringData
        .split(',')
        .map(name => name.trim())
        .filter(name => name !== '')
        .sort();
      return { names, count: names.length };
    }
    return { names: stringData ? [stringData] : [], count: stringData ? 1 : 0 };
  }
};

type SortField = 'timestamp' | 'audit_id' | 'strategy_name' | 'symbol' | 'side' | 'quantity' | 'price' | 'status' | 'strike';
type SortDirection = 'asc' | 'desc';

const StrategyAuditPage: React.FC = () => {
  const [auditTrails, setAuditTrails] = useState<SignalAuditTrail[]>([]);
  const [filteredAuditTrails, setFilteredAuditTrails] = useState<SignalAuditTrail[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortField, setSortField] = useState<SortField>('timestamp');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [filterParams, setFilterParams] = useState({
    audit_id: '',
    strategy_id: '',
    symbol: '',
    side: '',
    status: '',
    from_date: '',
    to_date: '',
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchAuditTrail();
  }, [ApiConfig.useMockData]);

  const fetchAuditTrail = async () => {
    try {
      setLoading(true);
      const response = await getStrategyAuditTrail({
        page: 1,
        limit: 100,
        ...filterParams
      });
      
      if (response.status === 'success') {
        setAuditTrails(response.data.data);
        setFilteredAuditTrails(response.data.data);
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to load strategy audit trail data",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFilter = (filters: Record<string, string>) => {
    if (Object.values(filters).every(value => value === '')) {
      setFilteredAuditTrails(auditTrails);
      return;
    }

    const apiFilterParams = {
      audit_id: filters.audit_id || '',
      strategy_id: filters.strategy_name || '',
      symbol: filters.symbol || '',
      side: filters.side || '',
      status: filters.status || '',
      from_date: filters.from_date || '',
      to_date: filters.to_date || '',
    };
    setFilterParams(apiFilterParams);

    const filtered = auditTrails.filter(trail => {
      return Object.entries(filters).every(([key, value]) => {
        if (!value) return true;

        if (key === 'search') {
          const searchValue = value.toLowerCase();
          return (
            trail.strategy_name.toLowerCase().includes(searchValue) ||
            trail.symbol.toLowerCase().includes(searchValue) ||
            trail.status.toLowerCase().includes(searchValue) ||
            trail.audit_id.toLowerCase().includes(searchValue) ||
            trail.side.toLowerCase().includes(searchValue)
          );
        }

        if (key === 'audit_id') {
          return trail.audit_id.toLowerCase().includes(value.toLowerCase());
        }

        if (key === 'strategy_name') {
          return trail.strategy_name.toLowerCase().includes(value.toLowerCase());
        }

        if (key === 'symbol') {
          return trail.symbol.toLowerCase().includes(value.toLowerCase());
        }

        if (key === 'side') {
          return trail.side.toLowerCase() === value.toLowerCase();
        }

        if (key === 'status') {
          return trail.status.toLowerCase() === value.toLowerCase();
        }

        if (key === 'from_date') {
          const trailDate = new Date(trail.timestamp);
          const filterDate = new Date(value);
          return trailDate >= filterDate;
        }

        if (key === 'to_date') {
          const trailDate = new Date(trail.timestamp);
          const filterDate = new Date(value);
          return trailDate <= filterDate;
        }

        return true;
      });
    });
    
    setFilteredAuditTrails(filtered);
  };

  const handleReset = () => {
    setFilterParams({
      audit_id: '',
      strategy_id: '',
      symbol: '',
      side: '',
      status: '',
      from_date: '',
      to_date: '',
    });
    setFilteredAuditTrails(auditTrails);
  };

  // Sorting functionality
  const handleSort = (field: SortField) => {
    const newDirection = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc';
    setSortField(field);
    setSortDirection(newDirection);

    const sorted = [...filteredAuditTrails].sort((a, b) => {
      let aValue: any = a[field];
      let bValue: any = b[field];

      // Handle timestamp sorting
      if (field === 'timestamp') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // Handle numeric fields
      if (field === 'quantity' || field === 'price') {
        aValue = parseFloat(aValue) || 0;
        bValue = parseFloat(bValue) || 0;
      }

      if (aValue < bValue) return newDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return newDirection === 'asc' ? 1 : -1;
      return 0;
    });

    setFilteredAuditTrails(sorted);
  };

  // Render sort icon
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />;
  };

  const filterFields = [
    { key: 'from_date', label: 'From Date', type: 'date' as const },
    { key: 'to_date', label: 'To Date', type: 'date' as const },
    { key: 'audit_id', label: 'Audit ID', type: 'text' as const },
    { key: 'strategy_name', label: 'Strategy Name', type: 'text' as const },
    { key: 'symbol', label: 'Symbol', type: 'text' as const },
    { key: 'side', label: 'Side', type: 'select' as const, options: [
      { value: 'BUY', label: 'BUY' },
      { value: 'SELL', label: 'SELL' },
    ]},
    { key: 'status', label: 'Status', type: 'select' as const, options: [
      { value: 'SUCCESS', label: 'SUCCESS' },
      { value: 'FAILED', label: 'FAILED' },
      { value: 'PARTIAL', label: 'PARTIAL' },
    ]}
  ];
  
  const handleApplyFilters = () => {
    fetchAuditTrail();
  };

  const getSideChipColor = (side: string) => {
    return side === 'BUY' ? 'bg-green-500 text-white' : 'bg-red-500 text-white';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-pulse text-xl">Loading strategy audit trail...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Activity size={24} className="text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Strategy API Audit Trail</h1>
            <p className="text-sm text-muted-foreground">Track strategy signal processing and client distribution</p>
          </div>
        </div>
        <Button variant="outline" onClick={fetchAuditTrail} disabled={loading}>
          <RefreshCw size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
          {loading ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      <Card className="p-6">
        <div className="space-y-6">
          <div className="flex flex-col space-y-4">
            <div className="flex items-center space-x-2 pb-2 border-b">
              <Filter size={18} className="text-primary" />
              <h3 className="text-lg font-semibold">Filters & Search</h3>
            </div>

            <DataTableFilter
              fields={filterFields}
              onFilter={handleFilter}
              onReset={handleReset}
            />

            <div className="flex justify-end">
              <Button
                variant="outline"
                onClick={handleApplyFilters}
                className="flex items-center"
              >
                <Filter size={16} className="mr-2" />
                Apply Filters
              </Button>
            </div>
          </div>

          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('timestamp')}
                  >
                    <div className="flex items-center space-x-2">
                      <Clock size={16} className="text-muted-foreground" />
                      <span>Timestamp</span>
                      {renderSortIcon('timestamp')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none min-w-[100px]"
                    onClick={() => handleSort('audit_id')}
                  >
                    <div className="flex items-center space-x-2">
                      <Hash size={16} className="text-muted-foreground" />
                      <span>Audit ID</span>
                      {renderSortIcon('audit_id')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('strategy_name')}
                  >
                    <div className="flex items-center space-x-2">
                      <Target size={16} className="text-muted-foreground" />
                      <span>Strategy</span>
                      {renderSortIcon('strategy_name')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('symbol')}
                  >
                    <div className="flex items-center space-x-2">
                      <TrendingUp size={16} className="text-muted-foreground" />
                      <span>Symbol</span>
                      {renderSortIcon('symbol')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('strike')}
                  >
                    <div className="flex items-center space-x-2">
                      <Target size={16} className="text-muted-foreground" />
                      <span>Strike</span>
                      {renderSortIcon('strike')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('side')}
                  >
                    <div className="flex items-center space-x-2">
                      <TrendingUp size={16} className="text-muted-foreground" />
                      <span>Side</span>
                      {renderSortIcon('side')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('quantity')}
                  >
                    <div className="flex items-center space-x-2">
                      <Package size={16} className="text-muted-foreground" />
                      <span>Quantity</span>
                      {renderSortIcon('quantity')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('price')}
                  >
                    <div className="flex items-center space-x-2">
                      <IndianRupee size={16} className="text-muted-foreground" />
                      <span>Price</span>
                      {renderSortIcon('price')}
                    </div>
                  </TableHead>
                  <TableHead>
                    <div className="flex items-center space-x-2">
                      <Calendar size={16} className="text-muted-foreground" />
                      <span>Expiry Type</span>
                    </div>
                  </TableHead>
                  <TableHead>
                    <div className="flex items-center space-x-2">
                      <Calendar size={16} className="text-muted-foreground" />
                      <span>Expiry Date</span>
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('status')}
                  >
                    <div className="flex items-center space-x-2">
                      <CheckCircle size={16} className="text-muted-foreground" />
                      <span>Status</span>
                      {renderSortIcon('status')}
                    </div>
                  </TableHead>
                  <TableHead className="min-w-[200px]">
                    <div className="flex items-center space-x-2">
                      <Users size={16} className="text-muted-foreground" />
                      <span>Eligible Clients</span>
                    </div>
                  </TableHead>
                  <TableHead>
                    <div className="flex items-center space-x-2">
                      <AlertCircle size={16} className="text-muted-foreground" />
                      <span>Reason / Error</span>
                    </div>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAuditTrails.length > 0 ? (
                  filteredAuditTrails.map((trail) => {
                    const clientsInfo = formatEligibleClients(trail.eligible_clients);

                    return (
                      <TableRow key={trail.audit_id}>
                        <TableCell className="font-medium">{formatTimestamp(trail.timestamp)}</TableCell>
                        <TableCell className="font-mono text-xs min-w-[100px] relative group">
                          <span className="truncate block w-[70px]">{trail.audit_id.substring(0, 5)}...</span>
                          <div className="absolute left-0 top-full z-10 mt-1 p-2 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none">
                            {trail.audit_id}
                          </div>
                        </TableCell>
                        <TableCell>{trail.strategy_name}</TableCell>
                        <TableCell>{trail.symbol}</TableCell>
                        <TableCell>{trail.strike || '-'}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSideChipColor(trail.side)}`}>
                            {trail.side}
                          </span>
                        </TableCell>
                        <TableCell>{trail.quantity}</TableCell>
                        <TableCell>₹{trail.price}</TableCell>
                        <TableCell>{trail.expiry_type || '-'}</TableCell>
                        <TableCell>{trail.expirydate || '-'}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusChipConfig(trail.status).className}`}>
                            {trail.status}
                          </span>
                        </TableCell>
                        <TableCell className="min-w-[200px]">
                          <div className="space-y-1">
                            <div className="text-sm font-medium text-muted-foreground">
                              {clientsInfo.count} Client{clientsInfo.count !== 1 ? 's' : ''}
                            </div>
                            {clientsInfo.names.length > 0 ? (
                              <div className="space-y-0.5">
                                {clientsInfo.names.map((name, index) => (
                                  <div key={index} className="text-xs flex items-center space-x-1">
                                    <span className="inline-flex items-center justify-center w-4 h-4 text-xs bg-primary/10 text-primary rounded-full">
                                      {index + 1}
                                    </span>
                                    <span className="truncate">{name}</span>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="text-xs text-muted-foreground">No clients</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="max-w-[200px] overflow-hidden">
                          {trail.reason || trail.error_message || '-'}
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={11} className="h-24 text-center">
                      No strategy audit trail records found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default StrategyAuditPage;
