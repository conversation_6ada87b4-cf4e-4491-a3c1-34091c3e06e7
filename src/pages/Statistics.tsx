
import React, { useState, useEffect, useMemo } from 'react';
import { useToast } from "@/hooks/use-toast";
import {
  getClients,
  getAllClientsPositions,
  getClientPositions,
  getBrokerClientOrders,
  getAllBrokerOrders,
  cancelOrder,
  modifyOrder,
  type StatisticsPosition,
  type BrokerOrder
} from '@/api/apiService';
import { Client } from '@/api/types';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RefreshCw, BarChart3, TrendingUp, Users, Package, DollarSign, Activity, Hash, Calendar, Building, X, Edit, AlertTriangle, Search, ArrowUpDown, ArrowUp, ArrowDown, Filter } from 'lucide-react';
import { getStatusChipConfig, getBuySellChipConfig } from '@/utils/orderTypeUtils';
import { isOrderModifiable } from '@/config/orderConfig';

// Transform API response to match component expectations
interface ClientPositionDisplay {
  client_id: string;
  client_name: string;
  total_positions: number;
  long_positions: number;
  pnl: number;
  unrealized: number;
  realized: number;
  positions: StatisticsPosition[];
}

// Utility function to format time only
const formatTime = (timestamp: string): string => {
  if (!timestamp) return 'N/A';
  try {
    if (/\d{2}-[A-Za-z]{3}-\d{4}/.test(timestamp)) {
      const [, timeStr] = timestamp.split(' ');
      return timeStr || timestamp;
    }
    return new Date(timestamp).toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  } catch (e) {
    return timestamp;
  }
};

const Statistics: React.FC = () => {
  const [selectedClientId, setSelectedClientId] = useState<string>('');
  const [selectedOrderClientId, setSelectedOrderClientId] = useState<string>('');
  const [clients, setClients] = useState<Client[]>([]);
  const [allPositions, setAllPositions] = useState<ClientPositionDisplay[]>([]);
  const [allOrders, setAllOrders] = useState<BrokerOrder[]>([]);
  const [clientPositions, setClientPositions] = useState<StatisticsPosition[]>([]);
  const [clientOrders, setClientOrders] = useState<BrokerOrder[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('all-positions');

  // Filtering and sorting states
  const [allPositionsFilter, setAllPositionsFilter] = useState<string>('');
  const [allPositionsSort, setAllPositionsSort] = useState<{field: string, direction: 'asc' | 'desc'}>({field: '', direction: 'asc'});
  const [clientPositionsFilter, setClientPositionsFilter] = useState<string>('');
  const [clientPositionsSort, setClientPositionsSort] = useState<{field: string, direction: 'asc' | 'desc'}>({field: '', direction: 'asc'});
  const [allOrdersFilter, setAllOrdersFilter] = useState<string>('');
  const [allOrdersSort, setAllOrdersSort] = useState<{field: string, direction: 'asc' | 'desc'}>({field: '', direction: 'asc'});
  const [clientOrdersFilter, setClientOrdersFilter] = useState<string>('');
  const [clientOrdersSort, setClientOrdersSort] = useState<{field: string, direction: 'asc' | 'desc'}>({field: '', direction: 'asc'});

  // Order action states
  const [modifyDialogOpen, setModifyDialogOpen] = useState<boolean>(false);
  const [selectedOrder, setSelectedOrder] = useState<BrokerOrder | null>(null);
  const [modifyForm, setModifyForm] = useState({
    price: '',
    qty: '',
    target: '',
    sl: '',
    trailing_sl: ''
  });



  const { toast } = useToast();

  // Sorting utility function
  const handleSort = (field: string, currentSort: {field: string, direction: 'asc' | 'desc'}, setSort: (sort: {field: string, direction: 'asc' | 'desc'}) => void) => {
    const direction = currentSort.field === field && currentSort.direction === 'asc' ? 'desc' : 'asc';
    setSort({field, direction});
  };

  // Generic sorting function
  const sortData = <T extends Record<string, any>>(data: T[], sortConfig: {field: string, direction: 'asc' | 'desc'}): T[] => {
    if (!sortConfig.field) return data;

    return [...data].sort((a, b) => {
      const aVal = a[sortConfig.field];
      const bVal = b[sortConfig.field];

      if (aVal === null || aVal === undefined) return 1;
      if (bVal === null || bVal === undefined) return -1;

      if (typeof aVal === 'string' && typeof bVal === 'string') {
        return sortConfig.direction === 'asc'
          ? aVal.localeCompare(bVal)
          : bVal.localeCompare(aVal);
      }

      if (typeof aVal === 'number' && typeof bVal === 'number') {
        return sortConfig.direction === 'asc' ? aVal - bVal : bVal - aVal;
      }

      return 0;
    });
  };

  // Generic filtering function
  const filterData = <T extends Record<string, any>>(data: T[], filter: string): T[] => {
    if (!filter) return data;

    return data.filter(item =>
      Object.values(item).some(value =>
        value?.toString().toLowerCase().includes(filter.toLowerCase())
      )
    );
  };

  // Memoized filtered and sorted data
  const filteredAndSortedAllPositions = useMemo(() => {
    const filtered = filterData(allPositions, allPositionsFilter);
    return sortData(filtered, allPositionsSort);
  }, [allPositions, allPositionsFilter, allPositionsSort]);

  const filteredAndSortedClientPositions = useMemo(() => {
    const filtered = filterData(clientPositions, clientPositionsFilter);
    return sortData(filtered, clientPositionsSort);
  }, [clientPositions, clientPositionsFilter, clientPositionsSort]);

  const filteredAndSortedAllOrders = useMemo(() => {
    const filtered = filterData(allOrders, allOrdersFilter);
    return sortData(filtered, allOrdersSort);
  }, [allOrders, allOrdersFilter, allOrdersSort]);

  const filteredAndSortedClientOrders = useMemo(() => {
    const filtered = filterData(clientOrders, clientOrdersFilter);
    return sortData(filtered, clientOrdersSort);
  }, [clientOrders, clientOrdersFilter, clientOrdersSort]);

  // Sortable header component
  const SortableHeader: React.FC<{
    field: string;
    children: React.ReactNode;
    sortConfig: {field: string, direction: 'asc' | 'desc'};
    onSort: (field: string) => void;
    icon?: React.ReactNode;
  }> = ({ field, children, sortConfig, onSort, icon }) => {
    const getSortIcon = () => {
      if (sortConfig.field !== field) return <ArrowUpDown size={14} className="text-muted-foreground" />;
      return sortConfig.direction === 'asc'
        ? <ArrowUp size={14} className="text-primary" />
        : <ArrowDown size={14} className="text-primary" />;
    };

    return (
      <th
        className="cursor-pointer hover:bg-muted/50 transition-colors"
        onClick={() => onSort(field)}
      >
        <div className="flex items-center space-x-2 justify-between">
          <div className="flex items-center space-x-2">
            {icon}
            <span>{children}</span>
          </div>
          {getSortIcon()}
        </div>
      </th>
    );
  };

  useEffect(() => {
    console.log('🚀 Component mounted, fetching clients...');
    fetchClients();
  }, []);

  useEffect(() => {
    console.log('👥 Clients state updated:', clients);
    console.log('👥 Clients count:', clients.length);
    if (clients.length > 0) {
      console.log('👥 First client:', clients[0]);
      console.log('👥 All client user_ids:', clients.map(c => c.user_id));

      // Auto-load all positions when clients are loaded and we're on the all-positions tab
      if (activeTab === 'all-positions') {
        console.log('🚀 Auto-loading all positions since clients are now available...');
        loadAllClientPositions();
      }
    }
  }, [clients]);

  useEffect(() => {
    console.log('📊 Active tab changed to:', activeTab);
  }, [activeTab]);

  const fetchClients = async () => {
    console.log('📡 Fetching clients...');
    try {
      const response = await getClients();
      console.log('📥 Clients API response:', response);

      if (response.status === 'success') {
        console.log('✅ Clients API successful');
        // Handle both direct array and paginated response
        let clientsData: Client[] = [];
        if (Array.isArray(response.data)) {
          console.log('📋 Response data is direct array');
          clientsData = response.data;
        } else if (response.data && Array.isArray(response.data.data)) {
          console.log('📋 Response data is paginated');
          clientsData = response.data.data;
        }

        console.log('👥 Processed clients data:', clientsData);
        console.log('👥 Clients count:', clientsData.length);

        setClients(clientsData);
        if (clientsData.length > 0) {
          console.log('👥 Setting default selected clients');
          setSelectedClientId(clientsData[0].user_id);
          setSelectedOrderClientId(clientsData[0].user_id);
        }
      } else {
        console.log('❌ Clients API failed:', response);
        toast({
          title: "Error",
          description: "Failed to fetch clients",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('💥 Error fetching clients:', error);
      toast({
        title: "Error",
        description: "Failed to fetch clients",
        variant: "destructive"
      });
    }
  };

  // Load all client positions
  const loadAllClientPositions = async () => {
    console.log('🔄 Starting loadAllClientPositions...');
    console.log('👥 Current clients state:', clients);
    console.log('📊 Current allPositions state:', allPositions);

    setLoading(true);
    try {
      console.log('📡 Calling getAllClientsPositions API...');
      const response = await getAllClientsPositions();
      console.log('📥 Raw API Response:', response);
      console.log('📥 Response.status:', response.status);
      console.log('📥 Response.data:', response.data);

      if (response.status !== 'success') {
        console.log('❌ API Response status not success:', response.status, response.message);
        toast({
          title: "Error",
          description: response.message || 'Failed to load positions',
          variant: "destructive"
        });
        return;
      }

      console.log('✅ API Response successful');
      console.log('📊 Response data structure:', response.data);
      console.log('👥 Clients in API response:', Object.keys(response.data.clients || {}));
      console.log('👥 Available clients in state:', clients.map(c => ({ user_id: c.user_id, name: c.name })));

      // Transform API response to component format
      const transformedPositions: ClientPositionDisplay[] = Object.values(response.data.clients || {}).map((client: any) => {
        console.log(`🔍 Processing client: ${client.user_id}`);
        console.log(`📋 Client data:`, client);

        // Find the client name from the clients list using user_id
        const clientInfo = clients.find(c => c.user_id === client.user_id);
        console.log(`👤 Found client info:`, clientInfo);

        const transformed = {
          client_id: client.user_id,
          client_name: clientInfo?.name || client.user_id,
          total_positions: client.summary.total_positions,
          long_positions: client.positions.filter((p: any) => p.qty > 0).length,
          pnl: client.summary.total_unrealized + client.summary.total_realized,
          unrealized: client.summary.total_unrealized,
          realized: client.summary.total_realized,
          positions: client.positions
        };
        console.log(`✨ Transformed client:`, transformed);
        return transformed;
      });

      console.log('🎯 Final transformed positions array:', transformedPositions);
      console.log('📈 Array length:', transformedPositions.length);
      console.log('📈 Setting allPositions state...');

      setAllPositions(transformedPositions);

      console.log('✅ allPositions state set successfully');
    } catch (error) {
      console.error('💥 Error in loadAllClientPositions:', error);
      toast({
        title: "Error",
        description: 'Failed to load positions. Please try again.',
        variant: "destructive"
      });
    } finally {
      setLoading(false);
      console.log('🏁 loadAllClientPositions completed');
    }
  };

  // Load client positions
  const loadClientPositions = async (clientId: string) => {
    if (!clientId) return;

    setLoading(true);
    try {
      const response = await getClientPositions(clientId);

      if (response.status !== 'success') {
        toast({
          title: "Error",
          description: response.message || 'Failed to load positions',
          variant: "destructive"
        });
        return;
      }

      console.log('Client Positions - Response Data:', response.data);
      console.log('Client Positions - Positions Array:', response.data.positions);
      setClientPositions(response.data.positions || []);
    } catch (error) {
      console.error('Error loading client positions:', error);
      toast({
        title: "Error",
        description: 'Failed to load positions. Please try again.',
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Load all client orders using broker orders API
  const loadAllClientOrders = async () => {
    console.log('🔄 Starting loadAllClientOrders...');
    console.log('👥 Current clients state:', clients);
    console.log('📋 Current allOrders state:', allOrders);

    setLoading(true);
    try {
      console.log('📡 Calling getAllBrokerOrders API...');
      const response = await getAllBrokerOrders();
      console.log('📥 All Orders API Response:', response);
      console.log('📥 Response.status:', response.status);
      console.log('📥 Response.data:', response.data);

      if (response.status !== 'success') {
        console.log('❌ All Orders API Response status not success:', response.status, response.message);
        toast({
          title: "Error",
          description: response.message || 'Failed to load orders',
          variant: "destructive"
        });
        return;
      }

      console.log('✅ All Orders API Response successful');
      console.log('📊 Orders response data structure:', response.data);
      console.log('👥 Clients in orders response:', Object.keys(response.data.clients || {}));
      console.log('👥 Available clients in state:', clients.map(c => ({ user_id: c.user_id, name: c.name })));

      // Flatten all orders
      const flattenedOrders: BrokerOrder[] = [];
      Object.values(response.data.clients || {}).forEach((clientOrder: any) => {
        console.log(`🔍 Processing orders for client: ${clientOrder.user_id}`);
        console.log(`📋 Client orders data:`, clientOrder);
        console.log(`📋 Client orders array:`, clientOrder.orders);
        console.log(`📋 Client orders array length:`, clientOrder.orders?.length || 0);

        // Find the client name from the clients list using user_id
        const clientInfo = clients.find(c => c.user_id === clientOrder.user_id);
        console.log(`👤 Found client info for orders:`, clientInfo);

        if (clientOrder.orders && Array.isArray(clientOrder.orders)) {
          console.log(`📋 Processing ${clientOrder.orders.length} orders for client ${clientOrder.user_id}`);
          clientOrder.orders.forEach((order: BrokerOrder, index: number) => {
            console.log(`📋 Processing order ${index + 1}:`, order);
            const enhancedOrder = {
              ...order,
              client_id: clientOrder.user_id,
              client_name: clientInfo?.name || clientOrder.user_id
            };
            console.log(`✨ Enhanced order:`, enhancedOrder);
            flattenedOrders.push(enhancedOrder);
          });
        } else {
          console.log(`⚠️ No orders array found for client ${clientOrder.user_id} or orders is not an array`);
        }
      });

      // Sort by time descending
      console.log('🔄 Sorting orders by timestamp...');
      flattenedOrders.sort((a, b) => {
        const timeA = new Date(b.timestamp).getTime();
        const timeB = new Date(a.timestamp).getTime();
        console.log(`📅 Comparing timestamps: ${b.timestamp} (${timeA}) vs ${a.timestamp} (${timeB})`);
        return timeA - timeB;
      });

      console.log('🎯 All Orders - Final Flattened Data:', flattenedOrders);
      console.log('📈 Orders array length:', flattenedOrders.length);
      console.log('📈 Orders array is empty?', flattenedOrders.length === 0);
      console.log('📈 Setting allOrders state...');
      setAllOrders(flattenedOrders);
      console.log('✅ allOrders state set successfully');

      // Additional debugging for empty orders
      if (flattenedOrders.length === 0) {
        console.log('⚠️ No orders found in any client - this will show "No orders found" message');
        console.log('📊 Total clients processed:', Object.keys(response.data.clients || {}).length);
        console.log('📊 Summary from API:', response.data.summary);
      }
    } catch (error) {
      console.error('💥 Error loading all client orders:', error);
      toast({
        title: "Error",
        description: 'Failed to load orders. Please try again.',
        variant: "destructive"
      });
    } finally {
      setLoading(false);
      console.log('🏁 loadAllClientOrders completed');
    }
  };

  // Load client orders using broker orders API
  const loadClientOrders = async (clientId: string) => {
    if (!clientId) {
      console.log('⚠️ No clientId provided for loadClientOrders');
      return;
    }

    console.log('🔄 Starting loadClientOrders for client:', clientId);
    console.log('📋 Current clientOrders state:', clientOrders);

    setLoading(true);
    try {
      console.log('📡 Calling getBrokerClientOrders API for client:', clientId);
      const response = await getBrokerClientOrders(clientId);
      console.log('📥 Client Orders API Response:', response);
      console.log('📥 Response.status:', response.status);
      console.log('📥 Response.data:', response.data);

      if (response.status !== 'success') {
        console.log('❌ Client Orders API Response status not success:', response.status, response.message);
        toast({
          title: "Error",
          description: response.message || 'Failed to load orders',
          variant: "destructive"
        });
        return;
      }

      console.log('✅ Client Orders API Response successful');
      console.log('📊 Client orders response data structure:', response.data);
      console.log('📋 Orders array from response:', response.data.orders);

      // Sort orders by time descending
      const orders = (response.data.orders || []).sort((a: BrokerOrder, b: BrokerOrder) => {
        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      });

      console.log('🎯 Client Orders - Final Sorted Data:', orders);
      console.log('📈 Client orders array length:', orders.length);
      console.log('📈 Setting clientOrders state...');
      setClientOrders(orders);
      console.log('✅ clientOrders state set successfully');
    } catch (error) {
      console.error('💥 Error loading client orders:', error);
      toast({
        title: "Error",
        description: 'Failed to load orders. Please try again.',
        variant: "destructive"
      });
    } finally {
      setLoading(false);
      console.log('🏁 loadClientOrders completed for client:', clientId);
    }
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    console.log('🔄 Tab change triggered:', value);
    console.log('🔄 Previous activeTab:', activeTab);
    setActiveTab(value);

    // Auto-load data when tab is selected
    switch (value) {
      case 'all-positions':
        console.log('📊 Loading all positions...');
        loadAllClientPositions();
        break;
      case 'client-positions':
        console.log('👤 Loading client positions for:', selectedClientId);
        if (selectedClientId) {
          loadClientPositions(selectedClientId);
        }
        break;
      case 'all-orders':
        console.log('📋 Loading all orders...');
        loadAllClientOrders();
        break;
      case 'client-orders':
        console.log('👤 Loading client orders for:', selectedOrderClientId);
        if (selectedOrderClientId) {
          loadClientOrders(selectedOrderClientId);
        }
        break;
    }
  };

  // Handle client selection for positions
  const handleClientSelect = (clientId: string) => {
    setSelectedClientId(clientId);
    if (clientId) {
      loadClientPositions(clientId);
    } else {
      setClientPositions([]);
    }
  };

  // Handle client selection for orders
  const handleOrderClientSelect = (clientId: string) => {
    setSelectedOrderClientId(clientId);
    if (clientId) {
      loadClientOrders(clientId);
    } else {
      setClientOrders([]);
    }
  };

  // Handle cancel order
  const handleCancelOrder = async (order: BrokerOrder) => {
    console.log('Cancelling order:', order);
    
    // Check if we have the required order information
    if (!order.order_id) {
      toast({
        title: "Error",
        description: "Invalid order: Missing order ID",
        variant: "destructive"
      });
      return;
    }

    // Check if we have client_id and broker, if not try to get them from the selected client
    const clientId = order.client_id || selectedOrderClientId;
    const broker = order.broker;

    if (!clientId || !broker) {
      console.error('Missing required information for order cancellation:', { 
        orderId: order.order_id,
        clientId: clientId || 'missing',
        broker: broker || 'missing',
        order
      });
      
      toast({
        title: "Error",
        description: `Cannot cancel order: ${!clientId ? 'Client ID' : 'Broker'} information is missing`,
        variant: "destructive"
      });
      return;
    }

    try {
      // Ensure we have the required fields for the API call
      // The BrokerOrder interface uses 'symbol' instead of 'tradingsymbol'
      const symbol = order.symbol || '';
      // Use the exchange from the order if available, otherwise default to 'NSE'
      const exchange = 'exchange' in order ? order.exchange : 'NSE';
      
      const response = await cancelOrder(
        clientId, 
        order.order_id, 
        broker,
        symbol,
        exchange
      );
      console.log('Cancel order response:', response);

      if (response.status === 'success') {
        toast({
          title: "Success",
          description: response.data?.message || "Order cancelled successfully"
        });
        
        // Refresh orders
        if (clientId) {
          loadClientOrders(clientId);
        } else if (selectedOrderClientId) {
          loadClientOrders(selectedOrderClientId);
        }
      } else {
        console.error('Failed to cancel order:', response);
        toast({
          title: "Error",
          description: response.data?.message || "Failed to cancel order",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error cancelling order:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to cancel order. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Handle modify order
  const handleModifyOrder = (order: BrokerOrder) => {
    setSelectedOrder(order);
    setModifyForm({
      price: order.price?.toString() || '',
      qty: order.qty?.toString() || '',
      target: order.target?.toString() || '',
      sl: order.sl?.toString() || '',
      trailing_sl: order.trailing_sl?.toString() || ''
    });
    setModifyDialogOpen(true);
  };

  // Submit modify order
  const submitModifyOrder = async () => {
    console.log('Modifying order:', selectedOrder, 'with changes:', modifyForm);
    
    // Check if we have the selected order
    if (!selectedOrder) {
      toast({
        title: "Error",
        description: "No order selected for modification",
        variant: "destructive"
      });
      return;
    }

    // Check if we have the required order information
    if (!selectedOrder.order_id) {
      toast({
        title: "Error",
        description: "Invalid order: Missing order ID",
        variant: "destructive"
      });
      return;
    }

    // Check if we have client_id and broker, if not try to get them from the selected client
    const clientId = selectedOrder.client_id || selectedOrderClientId;
    const broker = selectedOrder.broker;

    if (!clientId || !broker) {
      console.error('Missing required information for order modification:', { 
        orderId: selectedOrder.order_id,
        clientId: clientId || 'missing',
        broker: broker || 'missing',
        selectedOrder
      });
      
      toast({
        title: "Error",
        description: `Cannot modify order: ${!clientId ? 'Client ID' : 'Broker'} information is missing`,
        variant: "destructive"
      });
      return;
    }

    try {
      // Prepare modifications
      const modifications: any = {};
      if (modifyForm.price) modifications.price = parseFloat(modifyForm.price);
      if (modifyForm.qty) modifications.qty = parseInt(modifyForm.qty);
      if (modifyForm.target) modifications.target = parseFloat(modifyForm.target);
      if (modifyForm.sl) modifications.sl = parseFloat(modifyForm.sl);
      if (modifyForm.trailing_sl) modifications.trailing_sl = parseFloat(modifyForm.trailing_sl);

      console.log('Sending order modification:', { clientId, orderId: selectedOrder.order_id, broker, modifications });
      
      // Ensure we have the required fields for the API call
      // The BrokerOrder interface uses 'symbol' instead of 'tradingsymbol'
      const symbol = selectedOrder.symbol || '';
      // Use the exchange from the order if available, otherwise default to 'NSE'
      const exchange = 'exchange' in selectedOrder ? selectedOrder.exchange : 'NSE';
      
      const response = await modifyOrder(
        clientId,
        selectedOrder.order_id,
        broker,
        {
          ...modifications,
          tradingsymbol: symbol,
          exchange
        }
      );

      console.log('Modify order response:', response);

      if (response.status === 'success') {
        toast({
          title: "Success",
          description: response.data?.message || "Order modified successfully"
        });
        
        // Close the dialog and clear the form
        setModifyDialogOpen(false);
        setSelectedOrder(null);
        
        // Refresh orders
        if (clientId) {
          loadClientOrders(clientId);
        } else if (selectedOrderClientId) {
          loadClientOrders(selectedOrderClientId);
        }
      } else {
        console.error('Failed to modify order:', response);
        toast({
          title: "Error",
          description: response.data?.message || "Failed to modify order",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error modifying order:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to modify order. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleRefresh = () => {
    handleTabChange(activeTab);
  };



  // Calculate totals for all positions
  const allPositionsTotals = {
    totalClients: allPositions.length,
    totalPositions: allPositions.reduce((sum, client) => sum + (client.total_positions || 0), 0),
    totalUnrealized: allPositions.reduce((sum, client) => sum + (client.unrealized || 0), 0),
    totalRealized: allPositions.reduce((sum, client) => sum + (client.realized || 0), 0),
    totalPnl: allPositions.reduce((sum, client) => sum + (client.pnl || 0), 0)
  };

  // Calculate totals for client positions
  const clientPositionsTotals = {
    totalPositions: clientPositions.length,
    totalRealized: clientPositions.reduce((sum, pos) => sum + Number(pos.realized || 0), 0),
    totalUnrealized: clientPositions.reduce((sum, pos) => sum + Number(pos.unrealized || 0), 0),
    totalPnl: clientPositions.reduce((sum, pos) => {
      const pnl = Number(pos.unrealized || 0) + Number(pos.realized || 0);
      return sum + Number(pnl || 0);
    }, 0),
    totalBuyValue: clientPositions.reduce((sum, pos) => sum + (pos.buy_value || 0), 0),
    totalSellValue: clientPositions.reduce((sum, pos) => sum + (pos.sell_value || 0), 0)
  };

  // Calculate order status summary for all orders
  const allOrdersStatusSummary = useMemo(() => {
    const summary = {
      totalOrders: allOrders.length,
      open: 0,
      completed: 0,
      cancelled: 0,
      rejected: 0,
      pending: 0,
      failed: 0
    };

    allOrders.forEach(order => {
      const status = order.order_status?.toLowerCase() || '';
      if (['open', 'placed', 'trigger_pending', 'partially_filled', 'validated', 'queued', 'submitted', 'active'].includes(status)) {
        summary.open++;
      } else if (['complete', 'completed', 'filled', 'executed'].includes(status)) {
        summary.completed++;
      } else if (['cancelled', 'canceled'].includes(status)) {
        summary.cancelled++;
      } else if (['rejected'].includes(status)) {
        summary.rejected++;
      } else if (['pending'].includes(status)) {
        summary.pending++;
      } else if (['failed', 'expired'].includes(status)) {
        summary.failed++;
      }
    });

    return summary;
  }, [allOrders]);

  // Calculate order status summary for client orders
  const clientOrdersStatusSummary = useMemo(() => {
    const summary = {
      totalOrders: clientOrders.length,
      open: 0,
      completed: 0,
      cancelled: 0,
      rejected: 0,
      pending: 0,
      failed: 0
    };

    clientOrders.forEach(order => {
      const status = order.order_status?.toLowerCase() || '';
      if (['open', 'placed', 'trigger_pending', 'partially_filled', 'validated', 'queued', 'submitted', 'active'].includes(status)) {
        summary.open++;
      } else if (['complete', 'completed', 'filled', 'executed'].includes(status)) {
        summary.completed++;
      } else if (['cancelled', 'canceled'].includes(status)) {
        summary.cancelled++;
      } else if (['rejected'].includes(status)) {
        summary.rejected++;
      } else if (['pending'].includes(status)) {
        summary.pending++;
      } else if (['failed', 'expired'].includes(status)) {
        summary.failed++;
      }
    });

    return summary;
  }, [clientOrders]);

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Enhanced Page Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <BarChart3 size={24} className="text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Statistics</h1>
            <p className="text-sm text-muted-foreground">Monitor client positions and orders analytics</p>
          </div>
        </div>
        <Button variant="outline" onClick={handleRefresh} disabled={loading}>
          <RefreshCw size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
          {loading ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all-positions">All Client Positions</TabsTrigger>
          <TabsTrigger value="client-positions">Client Positions</TabsTrigger>
          <TabsTrigger value="all-orders">All Client Orders</TabsTrigger>
          <TabsTrigger value="client-orders">Client Orders</TabsTrigger>
        </TabsList>
          
        <TabsContent value="all-positions" className="mt-6">
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between pb-2 border-b mb-6">
                <div className="flex items-center space-x-2">
                  <Users size={18} className="text-primary" />
                  <h3 className="text-lg font-semibold">All Client Positions Summary</h3>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="Filter positions..."
                      value={allPositionsFilter}
                      onChange={(e) => setAllPositionsFilter(e.target.value)}
                      className="pl-9 w-64"
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter size={16} className="mr-2" />
                    Filter
                  </Button>
                </div>
              </div>

              {(() => {
                console.log('🎨 UI Render - Loading state:', loading);
                console.log('🎨 UI Render - allPositions state:', allPositions);
                console.log('🎨 UI Render - allPositions length:', allPositions.length);
                console.log('🎨 UI Render - filteredAndSortedAllPositions:', filteredAndSortedAllPositions);
                console.log('🎨 UI Render - filteredAndSortedAllPositions length:', filteredAndSortedAllPositions.length);
                return null;
              })()}

              {loading ? (
                <div className="flex justify-center items-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <span className="ml-3 text-muted-foreground">Loading client positions...</span>
                </div>
              ) : allPositions.length > 0 ? (
                <>
                  {/* Summary Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Users size={16} className="text-primary" />
                        <p className="text-sm text-muted-foreground">Total Clients</p>
                      </div>
                      <p className="text-2xl font-semibold">{allPositionsTotals.totalClients}</p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Package size={16} className="text-primary" />
                        <p className="text-sm text-muted-foreground">Total Positions</p>
                      </div>
                      <p className="text-2xl font-semibold">{allPositionsTotals.totalPositions}</p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Activity size={16} className="text-blue-600" />
                        <p className="text-sm text-muted-foreground">Total Unrealized</p>
                      </div>
                      <p className={`text-2xl font-semibold ${allPositionsTotals.totalUnrealized >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        ₹{Math.abs(allPositionsTotals.totalUnrealized).toFixed(2)} {allPositionsTotals.totalUnrealized >= 0 ? '▲' : '▼'}
                      </p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <TrendingUp size={16} className="text-green-600" />
                        <p className="text-sm text-muted-foreground">Total Realized</p>
                      </div>
                      <p className={`text-2xl font-semibold ${allPositionsTotals.totalRealized >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        ₹{Math.abs(allPositionsTotals.totalRealized).toFixed(2)} {allPositionsTotals.totalRealized >= 0 ? '▲' : '▼'}
                      </p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <DollarSign size={16} className="text-primary" />
                        <p className="text-sm text-muted-foreground">Total P&L</p>
                      </div>
                      <p className={`text-2xl font-semibold ${allPositionsTotals.totalPnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        ₹{Math.abs(allPositionsTotals.totalPnl).toFixed(2)} {allPositionsTotals.totalPnl >= 0 ? '▲' : '▼'}
                      </p>
                    </Card>
                  </div>

                  {/* Positions Table */}
                  <div className="table-container">
                    <table className="data-table">
                      <colgroup>
                        <col className="w-[15%]" />
                        <col className="w-[10%]" />
                        <col className="w-[10%]" />
                        <col className="w-[10%]" />
                        <col className="w-[10%]" />
                        <col className="w-[45%]" />
                      </colgroup>
                      <thead>
                        <tr>
                          <SortableHeader
                            field="client_name"
                            sortConfig={allPositionsSort}
                            onSort={(field) => handleSort(field, allPositionsSort, setAllPositionsSort)}
                            icon={<Users size={16} className="text-muted-foreground" />}
                          >
                            CLIENT
                          </SortableHeader>
                          <SortableHeader
                            field="total_positions"
                            sortConfig={allPositionsSort}
                            onSort={(field) => handleSort(field, allPositionsSort, setAllPositionsSort)}
                            icon={<Package size={16} className="text-muted-foreground" />}
                          >
                            TOTAL POSITIONS
                          </SortableHeader>
                          <SortableHeader
                            field="unrealized"
                            sortConfig={allPositionsSort}
                            onSort={(field) => handleSort(field, allPositionsSort, setAllPositionsSort)}
                            icon={<Activity size={16} className="text-muted-foreground" />}
                          >
                            UNREALIZED
                          </SortableHeader>
                          <SortableHeader
                            field="realized"
                            sortConfig={allPositionsSort}
                            onSort={(field) => handleSort(field, allPositionsSort, setAllPositionsSort)}
                            icon={<TrendingUp size={16} className="text-muted-foreground" />}
                          >
                            REALIZED
                          </SortableHeader>
                          <SortableHeader
                            field="pnl"
                            sortConfig={allPositionsSort}
                            onSort={(field) => handleSort(field, allPositionsSort, setAllPositionsSort)}
                            icon={<DollarSign size={16} className="text-muted-foreground" />}
                          >
                            P&L
                          </SortableHeader>
                          <th>
                            <div className="flex items-center space-x-2">
                              <Activity size={16} className="text-muted-foreground" />
                              <span>POSITIONS DETAIL</span>
                            </div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredAndSortedAllPositions.map((client, index) => {
                          const pnl = client.pnl || 0;
                          const unrealized = client.unrealized || 0;
                          const realized = client.realized || 0;
                          return (
                            <tr key={client.client_id || index} className="hover:bg-muted/50">
                              <td>
                                <div className="flex items-center space-x-3">
                                  <div className="flex-shrink-0 h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                                    <span className="text-primary font-medium">{client.client_name?.charAt(0)?.toUpperCase() || 'C'}</span>
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">{client.client_name}</div>
                                    <div className="text-sm text-muted-foreground">{client.client_id}</div>
                                  </div>
                                </div>
                              </td>
                              <td>
                                <div className="text-sm">{client.total_positions || 0}</div>
                                <div className="text-sm text-muted-foreground">{client.long_positions || 0} long</div>
                              </td>
                              <td>
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  unrealized >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                }`}>
                                  ₹{Math.abs(unrealized).toFixed(2)} {unrealized >= 0 ? '▲' : '▼'}
                                </span>
                              </td>
                              <td>
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  realized >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                }`}>
                                  ₹{Math.abs(realized).toFixed(2)} {realized >= 0 ? '▲' : '▼'}
                                </span>
                              </td>
                              <td>
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  pnl >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                }`}>
                                  ₹{Math.abs(pnl).toFixed(2)} {pnl >= 0 ? '▲' : '▼'}
                                </span>
                              </td>
                              <td>
                                <div className="space-y-2">
                                  {/* Position Details Sub-table */}
                                  {client.positions && client.positions.length > 0 ? (
                                    <div className="bg-muted/10 p-3 rounded-md border">
                                      <h5 className="text-xs font-medium mb-2 text-muted-foreground">Position Details ({client.positions.length})</h5>
                                      <div className="space-y-1 max-h-32 overflow-y-auto">
                                        {client.positions.slice(0, 3).map((position, posIndex) => {
                                          const positionPnl = Number(position.unrealized || 0) + Number(position.realized || 0);
                                          const isLong = position.qty > 0;
                                          return (
                                            <div key={posIndex} className="grid grid-cols-5 gap-2 text-xs p-2 bg-background rounded border items-center">
                                              <div className="flex items-center space-x-2">
                                                <div className={`flex-shrink-0 h-4 w-4 rounded-full flex items-center justify-center ${
                                                  isLong ? 'bg-green-100' : 'bg-red-100'
                                                }`}>
                                                  <span className={`text-xs font-medium ${isLong ? 'text-green-600' : 'text-red-600'}`}>
                                                    {position.tradingsymbol?.charAt(0)?.toUpperCase() || 'S'}
                                                  </span>
                                                </div>
                                                <span className="font-medium truncate">{position.tradingsymbol}</span>
                                              </div>
                                              <div className="text-right">
                                                <span>Q:{position.qty || 0}</span>
                                              </div>
                                              <div className="text-right">
                                                <span>B:₹{(position.buy_avg_price || 0).toFixed(1)}</span>
                                              </div>
                                              <div className="text-right">
                                                <span>S:₹{(position.sell_avg_price || 0).toFixed(1)}</span>
                                              </div>
                                              <div className="text-right">
                                                <span className={`px-1 py-0.5 rounded text-xs ${
                                                  positionPnl >= 0 ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                                                }`}>
                                                  ₹{Math.abs(positionPnl).toFixed(1)} {positionPnl >= 0 ? '▲' : '▼'}
                                                </span>
                                              </div>
                                            </div>
                                          );
                                        })}
                                        {client.positions.length > 3 && (
                                          <div className="text-xs text-muted-foreground text-center py-1">
                                            +{client.positions.length - 3} more positions
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  ) : (
                                    <div className="text-center p-3 bg-muted/10 rounded-md border">
                                      <Package size={16} className="mx-auto text-muted-foreground mb-1" />
                                      <p className="text-xs text-muted-foreground">No positions</p>
                                    </div>
                                  )}
                                  {/* Action Button */}
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      setActiveTab('client-positions');
                                      setSelectedClientId(client.client_id);
                                      loadClientPositions(client.client_id);
                                    }}
                                    className="text-xs w-full"
                                  >
                                    Open Client Positions →
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </>
              ) : (
                <div className="text-center p-6 bg-muted/30 rounded-md">
                  <Package size={48} className="mx-auto text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">No client positions found</p>
                  <p className="text-sm text-muted-foreground">Click refresh to load positions</p>
                </div>
              )}
            </div>
          </Card>
        </TabsContent>
          
        <TabsContent value="client-positions" className="mt-6">
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between pb-2 border-b mb-6">
                <div className="flex items-center space-x-2">
                  <Package size={18} className="text-primary" />
                  <h3 className="text-lg font-semibold">Client Positions</h3>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="Filter positions..."
                      value={clientPositionsFilter}
                      onChange={(e) => setClientPositionsFilter(e.target.value)}
                      className="pl-9 w-64"
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter size={16} className="mr-2" />
                    Filter
                  </Button>
                </div>
              </div>

              <div className="mb-6">
                <div className="w-full sm:w-80">
                  <label className="text-sm font-medium block mb-2">Select Client</label>
                  <Select value={selectedClientId} onValueChange={handleClientSelect}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Client" />
                    </SelectTrigger>
                    <SelectContent>
                      {clients.map((client) => (
                        <SelectItem key={client.id} value={client.user_id}>
                          {client.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {loading ? (
                <div className="flex justify-center items-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <span className="ml-3 text-muted-foreground">Loading positions...</span>
                </div>
              ) : selectedClientId ? (
                <>
                  {/* Summary Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Package size={16} className="text-primary" />
                        <p className="text-sm text-muted-foreground">Total Positions</p>
                      </div>
                      <p className="text-2xl font-semibold">{clientPositionsTotals.totalPositions}</p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <TrendingUp size={16} className="text-green-600" />
                        <p className="text-sm text-muted-foreground">Realised</p>
                      </div>
                      <p className={`text-2xl font-semibold ${clientPositionsTotals.totalRealized >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        ₹{Math.abs(clientPositionsTotals.totalRealized).toFixed(2)} {clientPositionsTotals.totalRealized >= 0 ? '▲' : '▼'}
                      </p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Activity size={16} className="text-blue-600" />
                        <p className="text-sm text-muted-foreground">Unrealized</p>
                      </div>
                      <p className={`text-2xl font-semibold ${clientPositionsTotals.totalUnrealized >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        ₹{Math.abs(clientPositionsTotals.totalUnrealized).toFixed(2)} {clientPositionsTotals.totalUnrealized >= 0 ? '▲' : '▼'}
                      </p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <DollarSign size={16} className="text-primary" />
                        <p className="text-sm text-muted-foreground">Total P&L</p>
                      </div>
                      <p className={`text-2xl font-semibold ${clientPositionsTotals.totalPnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        ₹{Math.abs(clientPositionsTotals.totalPnl).toFixed(2)} {clientPositionsTotals.totalPnl >= 0 ? '▲' : '▼'}
                      </p>
                    </Card>
                  </div>

                  {/* Positions Table */}
                  <div className="table-container">
                    <table className="data-table">
                      <thead>
                        <tr>
                          <SortableHeader
                            field="tradingsymbol"
                            sortConfig={clientPositionsSort}
                            onSort={(field) => handleSort(field, clientPositionsSort, setClientPositionsSort)}
                          >
                            SYMBOL
                          </SortableHeader>
                          <SortableHeader
                            field="exchange"
                            sortConfig={clientPositionsSort}
                            onSort={(field) => handleSort(field, clientPositionsSort, setClientPositionsSort)}
                          >
                            EXCHANGE
                          </SortableHeader>
                          <SortableHeader
                            field="product"
                            sortConfig={clientPositionsSort}
                            onSort={(field) => handleSort(field, clientPositionsSort, setClientPositionsSort)}
                          >
                            PRODUCT
                          </SortableHeader>
                          <SortableHeader
                            field="qty"
                            sortConfig={clientPositionsSort}
                            onSort={(field) => handleSort(field, clientPositionsSort, setClientPositionsSort)}
                          >
                            QTY
                          </SortableHeader>
                          <SortableHeader
                            field="ltp"
                            sortConfig={clientPositionsSort}
                            onSort={(field) => handleSort(field, clientPositionsSort, setClientPositionsSort)}
                          >
                            LTP
                          </SortableHeader>
                          <SortableHeader
                            field="buy_avg_price"
                            sortConfig={clientPositionsSort}
                            onSort={(field) => handleSort(field, clientPositionsSort, setClientPositionsSort)}
                          >
                            BUY AVG
                          </SortableHeader>
                          <SortableHeader
                            field="sell_avg_price"
                            sortConfig={clientPositionsSort}
                            onSort={(field) => handleSort(field, clientPositionsSort, setClientPositionsSort)}
                          >
                            SELL AVG
                          </SortableHeader>
                          <SortableHeader
                            field="unrealized"
                            sortConfig={clientPositionsSort}
                            onSort={(field) => handleSort(field, clientPositionsSort, setClientPositionsSort)}
                          >
                            UNREALIZED
                          </SortableHeader>
                          <SortableHeader
                            field="realized"
                            sortConfig={clientPositionsSort}
                            onSort={(field) => handleSort(field, clientPositionsSort, setClientPositionsSort)}
                          >
                            REALIZED
                          </SortableHeader>
                          <SortableHeader
                            field="pnl"
                            sortConfig={clientPositionsSort}
                            onSort={(field) => handleSort(field, clientPositionsSort, setClientPositionsSort)}
                          >
                            P&L
                          </SortableHeader>
                        </tr>
                      </thead>
                      <tbody>
                        {clientPositions.length > 0 ? (
                          filteredAndSortedClientPositions.map((position, index) => {
                            const pnl = Number(position.unrealized || 0) + Number(position.realized || 0);
                            const isLong = position.qty > 0;
                            return (
                              <tr key={index} className="hover:bg-muted/50">
                                <td>
                                  <div className="flex items-center space-x-2">
                                    <div className={`flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center ${
                                      isLong ? 'bg-green-100' : 'bg-red-100'
                                    }`}>
                                      <span className={`font-medium ${isLong ? 'text-green-600' : 'text-red-600'}`}>
                                        {position.tradingsymbol?.charAt(0)?.toUpperCase() || 'S'}
                                      </span>
                                    </div>
                                    <div>
                                      <div className="text-sm font-medium">{position.tradingsymbol}</div>
                                      <div className="text-sm text-muted-foreground">{position.exchange || 'NSE'}</div>
                                    </div>
                                  </div>
                                </td>
                                <td>{position.exchange || 'NSE'}</td>
                                <td>{position.product || 'MIS'}</td>
                                <td className="text-center">{position.qty || 0}</td>
                                <td className="text-right">₹{(position.ltp || 0).toFixed(2)}</td>
                                <td className="text-right">₹{(position.buy_avg_price || 0).toFixed(2)}</td>
                                <td className="text-right">₹{(position.sell_avg_price || 0).toFixed(2)}</td>
                                <td>
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    (position.unrealized || 0) >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    ₹{Math.abs(position.unrealized || 0).toFixed(2)} {(position.unrealized || 0) >= 0 ? '▲' : '▼'}
                                  </span>
                                </td>
                                <td>
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    (position.realized || 0) >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    ₹{Math.abs(position.realized || 0).toFixed(2)} {(position.realized || 0) >= 0 ? '▲' : '▼'}
                                  </span>
                                </td>
                                <td>
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    pnl >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    ₹{Math.abs(pnl).toFixed(2)} {pnl >= 0 ? '▲' : '▼'}
                                  </span>
                                </td>
                              </tr>
                            );
                          })
                        ) : (
                          <tr>
                            <td colSpan={10} className="text-center p-6 bg-muted/30">
                              <Package size={48} className="mx-auto text-muted-foreground mb-2" />
                              <p className="text-muted-foreground">No open positions found for this client</p>
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </>
              ) : (
                <div className="text-center p-6 bg-muted/30 rounded-md">
                  <Users size={48} className="mx-auto text-muted-foreground mb-2" />
                  <p className="text-muted-foreground">Select a client to view positions</p>
                </div>
              )}
            </div>
          </Card>
        </TabsContent>
          
        <TabsContent value="all-orders" className="mt-6">
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between pb-2 border-b mb-6">
                <div className="flex items-center space-x-2">
                  <Activity size={18} className="text-primary" />
                  <h3 className="text-lg font-semibold">All Client Orders</h3>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="Filter orders..."
                      value={allOrdersFilter}
                      onChange={(e) => setAllOrdersFilter(e.target.value)}
                      className="pl-9 w-64"
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter size={16} className="mr-2" />
                    Filter
                  </Button>
                </div>
              </div>

              {(() => {
                console.log('🎨 All Orders UI Render - Loading state:', loading);
                console.log('🎨 All Orders UI Render - allOrders state:', allOrders);
                console.log('🎨 All Orders UI Render - allOrders length:', allOrders.length);
                console.log('🎨 All Orders UI Render - filteredAndSortedAllOrders:', filteredAndSortedAllOrders);
                console.log('🎨 All Orders UI Render - filteredAndSortedAllOrders length:', filteredAndSortedAllOrders.length);

                // Debug the conditional logic
                if (loading) {
                  console.log('🎨 UI Decision: Showing loading spinner');
                } else if (allOrders.length > 0) {
                  console.log('🎨 UI Decision: Showing orders table');
                } else {
                  console.log('🎨 UI Decision: Showing "No orders found" message');
                }

                return null;
              })()}

              {loading ? (
                (() => {
                  console.log('🎨 Rendering: Loading spinner');
                  return (
                    <div className="flex justify-center items-center h-32">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                      <span className="ml-3 text-muted-foreground">Loading orders...</span>
                    </div>
                  );
                })()
              ) : allOrders.length > 0 ? (
                (() => {
                  console.log('🎨 Rendering: Orders table with', allOrders.length, 'orders');
                  return (
                <>
                  {/* Order Status Summary Cards */}
                  <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Activity size={16} className="text-primary" />
                        <p className="text-sm text-muted-foreground">Total Orders</p>
                      </div>
                      <p className="text-2xl font-semibold">{allOrdersStatusSummary.totalOrders}</p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Activity size={16} className="text-blue-600" />
                        <p className="text-sm text-muted-foreground">Open</p>
                      </div>
                      <p className="text-2xl font-semibold text-blue-600">{allOrdersStatusSummary.open}</p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Activity size={16} className="text-green-600" />
                        <p className="text-sm text-muted-foreground">Completed</p>
                      </div>
                      <p className="text-2xl font-semibold text-green-600">{allOrdersStatusSummary.completed}</p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Activity size={16} className="text-orange-600" />
                        <p className="text-sm text-muted-foreground">Pending</p>
                      </div>
                      <p className="text-2xl font-semibold text-orange-600">{allOrdersStatusSummary.pending}</p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Activity size={16} className="text-gray-600" />
                        <p className="text-sm text-muted-foreground">Cancelled</p>
                      </div>
                      <p className="text-2xl font-semibold text-gray-600">{allOrdersStatusSummary.cancelled}</p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Activity size={16} className="text-red-600" />
                        <p className="text-sm text-muted-foreground">Rejected</p>
                      </div>
                      <p className="text-2xl font-semibold text-red-600">{allOrdersStatusSummary.rejected}</p>
                    </Card>
                  </div>

                  {/* Orders Table */}
                  <div className="table-container">
                    <table className="data-table">
                      <thead>
                        <tr>
                          <SortableHeader
                            field="client_name"
                            sortConfig={allOrdersSort}
                            onSort={(field) => handleSort(field, allOrdersSort, setAllOrdersSort)}
                            icon={<Users size={16} className="text-muted-foreground" />}
                          >
                            CLIENT
                          </SortableHeader>
                          <SortableHeader
                            field="order_id"
                            sortConfig={allOrdersSort}
                            onSort={(field) => handleSort(field, allOrdersSort, setAllOrdersSort)}
                            icon={<Hash size={16} className="text-muted-foreground" />}
                          >
                            ORDER ID
                          </SortableHeader>
                          <SortableHeader
                            field="time"
                            sortConfig={allOrdersSort}
                            onSort={(field) => handleSort(field, allOrdersSort, setAllOrdersSort)}
                            icon={<Calendar size={16} className="text-muted-foreground" />}
                          >
                            TIME
                          </SortableHeader>
                          <SortableHeader
                            field="symbol"
                            sortConfig={allOrdersSort}
                            onSort={(field) => handleSort(field, allOrdersSort, setAllOrdersSort)}
                          >
                            SYMBOL
                          </SortableHeader>
                          <SortableHeader
                            field="buy_sell"
                            sortConfig={allOrdersSort}
                            onSort={(field) => handleSort(field, allOrdersSort, setAllOrdersSort)}
                          >
                            TYPE
                          </SortableHeader>
                          <SortableHeader
                            field="variety"
                            sortConfig={allOrdersSort}
                            onSort={(field) => handleSort(field, allOrdersSort, setAllOrdersSort)}
                          >
                            VARIETY
                          </SortableHeader>
                          <SortableHeader
                            field="qty"
                            sortConfig={allOrdersSort}
                            onSort={(field) => handleSort(field, allOrdersSort, setAllOrdersSort)}
                          >
                            QUANTITY
                          </SortableHeader>
                          <SortableHeader
                            field="price"
                            sortConfig={allOrdersSort}
                            onSort={(field) => handleSort(field, allOrdersSort, setAllOrdersSort)}
                          >
                            PRICE
                          </SortableHeader>
                          <SortableHeader
                            field="target"
                            sortConfig={allOrdersSort}
                            onSort={(field) => handleSort(field, allOrdersSort, setAllOrdersSort)}
                          >
                            TARGET
                          </SortableHeader>
                          <SortableHeader
                            field="sl"
                            sortConfig={allOrdersSort}
                            onSort={(field) => handleSort(field, allOrdersSort, setAllOrdersSort)}
                          >
                            SL
                          </SortableHeader>
                          <SortableHeader
                            field="trailing_sl"
                            sortConfig={allOrdersSort}
                            onSort={(field) => handleSort(field, allOrdersSort, setAllOrdersSort)}
                          >
                            TRAILING SL
                          </SortableHeader>
                          <SortableHeader
                            field="order_status"
                            sortConfig={allOrdersSort}
                            onSort={(field) => handleSort(field, allOrdersSort, setAllOrdersSort)}
                          >
                            ORDER STATUS
                          </SortableHeader>
                          <SortableHeader
                            field="reason"
                            sortConfig={allOrdersSort}
                            onSort={(field) => handleSort(field, allOrdersSort, setAllOrdersSort)}
                          >
                            REASON
                          </SortableHeader>
                          <SortableHeader
                            field="broker"
                            sortConfig={allOrdersSort}
                            onSort={(field) => handleSort(field, allOrdersSort, setAllOrdersSort)}
                            icon={<Building size={16} className="text-muted-foreground" />}
                          >
                            BROKER
                          </SortableHeader>
                          <th>
                            <div className="flex items-center space-x-2">
                              <span>ACTIONS</span>
                            </div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredAndSortedAllOrders.map((order, index) => (
                          <tr key={order.order_id || index} className="hover:bg-muted/50">
                            <td>
                              <div className="text-sm text-primary hover:text-primary/80 cursor-pointer"
                                   onClick={() => {
                                     setActiveTab('client-orders');
                                     setSelectedOrderClientId(order.client_id || '');
                                     if (order.client_id) loadClientOrders(order.client_id);
                                   }}>
                                {order.client_name}
                              </div>
                            </td>
                            <td className="font-mono text-sm">{order.order_id}</td>
                            <td>{formatTime(order.time || order.timestamp)}</td>
                            <td>{order.symbol}</td>
                            <td>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getBuySellChipConfig(order.buy_sell).className}`}>
                                {order.buy_sell}
                              </span>
                            </td>
                            <td>{order.variety || 'N/A'}</td>
                            <td className="text-center">{order.qty}</td>
                            <td className="text-right">₹{Number(order.price || 0).toFixed(2)}</td>
                            <td className="text-right">{order.target ? `₹${Number(order.target).toFixed(2)}` : 'N/A'}</td>
                            <td className="text-right">{order.sl ? `₹${Number(order.sl).toFixed(2)}` : 'N/A'}</td>
                            <td className="text-right">{order.trailing_sl ? `₹${Number(order.trailing_sl).toFixed(2)}` : 'N/A'}</td>
                            <td>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusChipConfig(order.order_status || order.status).className}`}>
                                {order.order_status || order.status}
                              </span>
                            </td>
                            <td className="text-sm max-w-32 truncate" title={order.reason || 'N/A'}>
                              {order.reason || 'N/A'}
                            </td>
                            <td>
                              <div className="flex items-center space-x-1">
                                <Building size={12} className="text-muted-foreground" />
                                <span className="text-sm">{order.broker}</span>
                              </div>
                            </td>
                            <td>
                              <div className="flex items-center space-x-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleModifyOrder(order)}
                                  className="h-8 px-2 text-xs"
                                  disabled={!isOrderModifiable(order.order_status || order.status)}
                                >
                                  <Edit size={12} className="mr-1" />
                                  Modify
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleCancelOrder(order)}
                                  className="h-8 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
                                  disabled={!isOrderModifiable(order.order_status || order.status)}
                                >
                                  <X size={12} className="mr-1" />
                                  Cancel
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </>
                  );
                })()
              ) : (
                (() => {
                  console.log('🎨 Rendering: No orders found message');
                  return (
                    <div className="text-center p-6 bg-muted/30 rounded-md">
                      <Activity size={48} className="mx-auto text-muted-foreground mb-2" />
                      <p className="text-muted-foreground">No orders found</p>
                      <p className="text-sm text-muted-foreground">Click refresh to load orders</p>
                    </div>
                  );
                })()
              )}
            </div>
          </Card>
        </TabsContent>
          
        <TabsContent value="client-orders" className="mt-6">
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between pb-2 border-b mb-6">
                <div className="flex items-center space-x-2">
                  <Building size={18} className="text-primary" />
                  <h3 className="text-lg font-semibold">Client Orders</h3>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="Filter orders..."
                      value={clientOrdersFilter}
                      onChange={(e) => setClientOrdersFilter(e.target.value)}
                      className="pl-9 w-64"
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter size={16} className="mr-2" />
                    Filter
                  </Button>
                </div>
              </div>

              <div className="mb-6">
                <div className="w-full sm:w-80">
                  <label className="text-sm font-medium block mb-2">Select Client</label>
                  <Select value={selectedOrderClientId} onValueChange={handleOrderClientSelect}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Client" />
                    </SelectTrigger>
                    <SelectContent>
                      {clients.map((client) => (
                        <SelectItem key={client.id} value={client.user_id}>
                          {client.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {(() => {
                console.log('🎨 Client Orders UI Render - Loading state:', loading);
                console.log('🎨 Client Orders UI Render - selectedOrderClientId:', selectedOrderClientId);
                console.log('🎨 Client Orders UI Render - clientOrders state:', clientOrders);
                console.log('🎨 Client Orders UI Render - clientOrders length:', clientOrders.length);
                console.log('🎨 Client Orders UI Render - filteredAndSortedClientOrders:', filteredAndSortedClientOrders);
                console.log('🎨 Client Orders UI Render - filteredAndSortedClientOrders length:', filteredAndSortedClientOrders.length);

                // Debug the conditional logic
                if (loading) {
                  console.log('🎨 Client Orders UI Decision: Showing loading spinner');
                } else if (clientOrders.length > 0) {
                  console.log('🎨 Client Orders UI Decision: Showing orders table');
                } else if (selectedOrderClientId) {
                  console.log('🎨 Client Orders UI Decision: Showing "No orders found for this client" message');
                } else {
                  console.log('🎨 Client Orders UI Decision: Showing "Select a client" message');
                }

                return null;
              })()}

              {loading ? (
                (() => {
                  console.log('🎨 Client Orders Rendering: Loading spinner');
                  return (
                    <div className="flex justify-center items-center h-32">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                      <span className="ml-3 text-muted-foreground">Loading orders...</span>
                    </div>
                  );
                })()
              ) : clientOrders.length > 0 ? (
                (() => {
                  console.log('🎨 Client Orders Rendering: Orders table with', clientOrders.length, 'orders');
                  return (
                <>
                  {/* Order Status Summary Cards */}
                  <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Activity size={16} className="text-primary" />
                        <p className="text-sm text-muted-foreground">Total Orders</p>
                      </div>
                      <p className="text-2xl font-semibold">{clientOrdersStatusSummary.totalOrders}</p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Activity size={16} className="text-blue-600" />
                        <p className="text-sm text-muted-foreground">Open</p>
                      </div>
                      <p className="text-2xl font-semibold text-blue-600">{clientOrdersStatusSummary.open}</p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Activity size={16} className="text-green-600" />
                        <p className="text-sm text-muted-foreground">Completed</p>
                      </div>
                      <p className="text-2xl font-semibold text-green-600">{clientOrdersStatusSummary.completed}</p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Activity size={16} className="text-orange-600" />
                        <p className="text-sm text-muted-foreground">Pending</p>
                      </div>
                      <p className="text-2xl font-semibold text-orange-600">{clientOrdersStatusSummary.pending}</p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Activity size={16} className="text-gray-600" />
                        <p className="text-sm text-muted-foreground">Cancelled</p>
                      </div>
                      <p className="text-2xl font-semibold text-gray-600">{clientOrdersStatusSummary.cancelled}</p>
                    </Card>
                    <Card className="p-4">
                      <div className="flex items-center space-x-2">
                        <Activity size={16} className="text-red-600" />
                        <p className="text-sm text-muted-foreground">Rejected</p>
                      </div>
                      <p className="text-2xl font-semibold text-red-600">{clientOrdersStatusSummary.rejected}</p>
                    </Card>
                  </div>

                  {/* Orders Table */}
                  <div className="table-container">
                    <table className="data-table">
                      <thead>
                        <tr>
                          <SortableHeader
                            field="order_id"
                            sortConfig={clientOrdersSort}
                            onSort={(field) => handleSort(field, clientOrdersSort, setClientOrdersSort)}
                            icon={<Hash size={16} className="text-muted-foreground" />}
                          >
                            ORDER ID
                          </SortableHeader>
                          <SortableHeader
                            field="time"
                            sortConfig={clientOrdersSort}
                            onSort={(field) => handleSort(field, clientOrdersSort, setClientOrdersSort)}
                            icon={<Calendar size={16} className="text-muted-foreground" />}
                          >
                            TIME
                          </SortableHeader>
                          <SortableHeader
                            field="symbol"
                            sortConfig={clientOrdersSort}
                            onSort={(field) => handleSort(field, clientOrdersSort, setClientOrdersSort)}
                          >
                            SYMBOL
                          </SortableHeader>
                          <SortableHeader
                            field="buy_sell"
                            sortConfig={clientOrdersSort}
                            onSort={(field) => handleSort(field, clientOrdersSort, setClientOrdersSort)}
                          >
                            TYPE
                          </SortableHeader>
                          <SortableHeader
                            field="variety"
                            sortConfig={clientOrdersSort}
                            onSort={(field) => handleSort(field, clientOrdersSort, setClientOrdersSort)}
                          >
                            VARIETY
                          </SortableHeader>
                          <SortableHeader
                            field="qty"
                            sortConfig={clientOrdersSort}
                            onSort={(field) => handleSort(field, clientOrdersSort, setClientOrdersSort)}
                          >
                            QUANTITY
                          </SortableHeader>
                          <SortableHeader
                            field="price"
                            sortConfig={clientOrdersSort}
                            onSort={(field) => handleSort(field, clientOrdersSort, setClientOrdersSort)}
                          >
                            PRICE
                          </SortableHeader>
                          <SortableHeader
                            field="target"
                            sortConfig={clientOrdersSort}
                            onSort={(field) => handleSort(field, clientOrdersSort, setClientOrdersSort)}
                          >
                            TARGET
                          </SortableHeader>
                          <SortableHeader
                            field="sl"
                            sortConfig={clientOrdersSort}
                            onSort={(field) => handleSort(field, clientOrdersSort, setClientOrdersSort)}
                          >
                            SL
                          </SortableHeader>
                          <SortableHeader
                            field="trailing_sl"
                            sortConfig={clientOrdersSort}
                            onSort={(field) => handleSort(field, clientOrdersSort, setClientOrdersSort)}
                          >
                            TRAILING SL
                          </SortableHeader>
                          <SortableHeader
                            field="order_status"
                            sortConfig={clientOrdersSort}
                            onSort={(field) => handleSort(field, clientOrdersSort, setClientOrdersSort)}
                          >
                            ORDER STATUS
                          </SortableHeader>
                          <SortableHeader
                            field="reason"
                            sortConfig={clientOrdersSort}
                            onSort={(field) => handleSort(field, clientOrdersSort, setClientOrdersSort)}
                          >
                            REASON
                          </SortableHeader>
                          <SortableHeader
                            field="broker"
                            sortConfig={clientOrdersSort}
                            onSort={(field) => handleSort(field, clientOrdersSort, setClientOrdersSort)}
                            icon={<Building size={16} className="text-muted-foreground" />}
                          >
                            BROKER
                          </SortableHeader>
                          <th>
                            <div className="flex items-center space-x-2">
                              <span>ACTIONS</span>
                            </div>
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredAndSortedClientOrders.map((order, index) => (
                          <tr key={order.order_id || index} className="hover:bg-muted/50">
                            <td className="font-mono text-sm">{order.order_id}</td>
                            <td>{formatTime(order.time || order.timestamp)}</td>
                            <td>{order.symbol}</td>
                            <td>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getBuySellChipConfig(order.buy_sell).className}`}>
                                {order.buy_sell}
                              </span>
                            </td>
                            <td>{order.variety || 'N/A'}</td>
                            <td className="text-center">{order.qty}</td>
                            <td className="text-right">₹{Number(order.price || 0).toFixed(2)}</td>
                            <td className="text-right">{order.target ? `₹${Number(order.target).toFixed(2)}` : 'N/A'}</td>
                            <td className="text-right">{order.sl ? `₹${Number(order.sl).toFixed(2)}` : 'N/A'}</td>
                            <td className="text-right">{order.trailing_sl ? `₹${Number(order.trailing_sl).toFixed(2)}` : 'N/A'}</td>
                            <td>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusChipConfig(order.order_status || order.status).className}`}>
                                {order.order_status || order.status}
                              </span>
                            </td>
                            <td className="text-sm max-w-32 truncate" title={order.reason || 'N/A'}>
                              {order.reason || 'N/A'}
                            </td>
                            <td>
                              <div className="flex items-center space-x-1">
                                <Building size={12} className="text-muted-foreground" />
                                <span className="text-sm">{order.broker}</span>
                              </div>
                            </td>
                            <td>
                              <div className="flex items-center space-x-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleModifyOrder(order)}
                                  className="h-8 px-2 text-xs"
                                  disabled={!isOrderModifiable(order.order_status || order.status)}
                                >
                                  <Edit size={12} className="mr-1" />
                                  Modify
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleCancelOrder(order)}
                                  className="h-8 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
                                  disabled={!isOrderModifiable(order.order_status || order.status)}
                                >
                                  <X size={12} className="mr-1" />
                                  Cancel
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </>
                  );
                })()
              ) : selectedOrderClientId ? (
                (() => {
                  console.log('🎨 Client Orders Rendering: No orders found for client message');
                  return (
                    <div className="text-center p-6 bg-muted/30 rounded-md">
                      <Activity size={48} className="mx-auto text-muted-foreground mb-2" />
                      <p className="text-muted-foreground">No orders found for this client</p>
                    </div>
                  );
                })()
              ) : (
                (() => {
                  console.log('🎨 Client Orders Rendering: Select client message');
                  return (
                    <div className="text-center p-6 bg-muted/30 rounded-md">
                      <Users size={48} className="mx-auto text-muted-foreground mb-2" />
                      <p className="text-muted-foreground">Select a client to view orders</p>
                    </div>
                  );
                })()
              )}
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modify Order Dialog */}
      <Dialog open={modifyDialogOpen} onOpenChange={setModifyDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Edit size={20} />
              <span>Modify Order</span>
            </DialogTitle>
          </DialogHeader>
          {selectedOrder && (
            <div className="space-y-4">
              <div className="bg-muted/30 p-3 rounded-md">
                <p className="text-sm font-medium">Order: {selectedOrder.order_id}</p>
                <p className="text-sm text-muted-foreground">Symbol: {selectedOrder.symbol}</p>
                <p className="text-sm text-muted-foreground">Type: {selectedOrder.buy_sell}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="price">Price</Label>
                  <Input
                    id="price"
                    type="number"
                    step="0.01"
                    value={modifyForm.price}
                    onChange={(e) => setModifyForm(prev => ({ ...prev, price: e.target.value }))}
                    placeholder="Enter price"
                  />
                </div>
                <div>
                  <Label htmlFor="qty">Quantity</Label>
                  <Input
                    id="qty"
                    type="number"
                    value={modifyForm.qty}
                    onChange={(e) => setModifyForm(prev => ({ ...prev, qty: e.target.value }))}
                    placeholder="Enter quantity"
                  />
                </div>
              </div>

              {selectedOrder.broker === 'angleone' && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="target">Target Price</Label>
                    <Input
                      id="target"
                      type="number"
                      step="0.01"
                      value={modifyForm.target}
                      onChange={(e) => setModifyForm(prev => ({ ...prev, target: e.target.value }))}
                      placeholder="Enter target"
                    />
                  </div>
                  <div>
                    <Label htmlFor="sl">Stop Loss</Label>
                    <Input
                      id="sl"
                      type="number"
                      step="0.01"
                      value={modifyForm.sl}
                      onChange={(e) => setModifyForm(prev => ({ ...prev, sl: e.target.value }))}
                      placeholder="Enter stop loss"
                    />
                  </div>
                </div>
              )}

              {selectedOrder.broker === 'angleone' && (
                <div>
                  <Label htmlFor="trailing_sl">Trailing Stop Loss</Label>
                  <Input
                    id="trailing_sl"
                    type="number"
                    step="0.01"
                    value={modifyForm.trailing_sl}
                    onChange={(e) => setModifyForm(prev => ({ ...prev, trailing_sl: e.target.value }))}
                    placeholder="Enter trailing SL"
                  />
                </div>
              )}

              <div className="flex items-center space-x-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <AlertTriangle size={16} className="text-yellow-600" />
                <p className="text-sm text-yellow-800">
                  Only modify fields you want to change. Leave others empty.
                </p>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setModifyDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={submitModifyOrder}>
                  Modify Order
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>


    </div>
  );
};

export default Statistics;
