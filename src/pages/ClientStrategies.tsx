
import React, { useState, useEffect } from 'react';
import { useToast } from "@/hooks/use-toast";
import { getClients, getStrategies } from '@/api/apiService';
import { getClientStrategies, createClientStrategy, updateClientStrategy, deleteClientStrategy } from '@/services/ClientStrategyService';
import { Client, Strategy, ClientStrategy } from '@/api/types';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Plus, Edit, Trash2, RefreshCw, Eye, Filter, Users, Target, Settings, ToggleLeft, Calendar, Wrench, ChevronUp, ChevronDown } from 'lucide-react';
import DataTableFilter from '@/components/Filters/DataTableFilter';
import ClientStrategyRulesDialog from '@/components/Dialogs/ClientStrategyRulesDialog';
import { ApiConfig } from '@/api/config';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

const clientStrategySchema = z.object({
  client_id: z.string().min(1, "Client is required"),
  strategy_id: z.string().min(1, "Strategy is required"),
  variety: z.enum(['NORMAL', 'ROBO']),
  is_enabled: z.boolean(),
  preference_type: z.enum(['AS_RECEIVED', 'FORCE_BUY', 'FORCE_SELL']),
  preferred_instrument: z.enum(['ANY', 'EQ', 'FUTURE', 'CE', 'PE']),
});

type ClientStrategyFormValues = z.infer<typeof clientStrategySchema>;

// Utility function to format timestamp (DD-MM-YYYY HH:MI AM/PM)
const formatTimestamp = (timestamp: string): string => {
  try {
    const date = new Date(timestamp);
    return date.toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).replace(',', '');
  } catch (e) {
    // If parsing fails, try to remove timezone info manually
    return timestamp.replace(/\s*GMT.*$/, '').replace(/\s*\+.*$/, '').replace(/T.*Z$/, '');
  }
};

type SortField = 'clientName' | 'strategyName' | 'variety' | 'preference_type' | 'preferred_instrument' | 'is_enabled' | 'created_at';
type SortDirection = 'asc' | 'desc';

const ClientStrategies: React.FC = () => {
  const [clientStrategies, setClientStrategies] = useState<ClientStrategy[]>([]);
  const [filteredClientStrategies, setFilteredClientStrategies] = useState<ClientStrategy[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortField, setSortField] = useState<SortField>('created_at');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedClientStrategy, setSelectedClientStrategy] = useState<ClientStrategy | null>(null);
  const [isRulesDialogOpen, setIsRulesDialogOpen] = useState(false);
  const [selectedClientStrategyForRules, setSelectedClientStrategyForRules] = useState<ClientStrategy | null>(null);
  const { toast } = useToast();

  const form = useForm<ClientStrategyFormValues>({
    resolver: zodResolver(clientStrategySchema),
    defaultValues: {
      client_id: "",
      strategy_id: "",
      variety: "NORMAL",
      is_enabled: true,
      preference_type: "AS_RECEIVED",
      preferred_instrument: "ANY",
    },
  });

  useEffect(() => {
    fetchData();
  }, [ApiConfig.useMockData]);

  const fetchData = async () => {
    try {
      setLoading(true);
      console.log("Fetching client strategies, clients, and strategies...");
      const [clientStrategiesResponse, clientsResponse, strategiesResponse] = await Promise.all([
        getClientStrategies(),
        getClients(),
        getStrategies()
      ]);

      if (clientStrategiesResponse.status === 'success') {
        const enrichedClientStrategies = clientStrategiesResponse.data.data.map(cs => {
          const client = clientsResponse.status === 'success' 
            ? clientsResponse.data.data.find(c => c.id === cs.client_id)
            : null;
          const strategy = strategiesResponse.status === 'success'
            ? strategiesResponse.data.data.find(s => s.id === cs.strategy_id)
            : null;
          
          return {
            ...cs,
            clientName: client?.name || 'Unknown Client',
            strategyName: strategy?.name || 'Unknown Strategy'
          };
        });
        
        setClientStrategies(enrichedClientStrategies);
        setFilteredClientStrategies(enrichedClientStrategies);
      }

      if (clientsResponse.status === 'success') {
        setClients(clientsResponse.data.data);
      }

      if (strategiesResponse.status === 'success') {
        setStrategies(strategiesResponse.data.data);
      }

    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFilter = (filters: Record<string, string>) => {
    if (Object.values(filters).every(value => value === '')) {
      setFilteredClientStrategies(clientStrategies);
      return;
    }

    const filtered = clientStrategies.filter(cs => {
      return Object.entries(filters).every(([key, value]) => {
        if (!value) return true;
        
        if (key === 'search') {
          const searchValue = value.toLowerCase();
          return (
            (cs.clientName || '').toLowerCase().includes(searchValue) ||
            (cs.strategyName || '').toLowerCase().includes(searchValue) ||
            cs.variety.toLowerCase().includes(searchValue)
          );
        }
        
        const csValue = (cs as any)[key];
        if (!csValue) return false;
        return csValue.toString().toLowerCase().includes(value.toLowerCase());
      });
    });
    
    setFilteredClientStrategies(filtered);
  };

  const handleReset = () => {
    setFilteredClientStrategies(clientStrategies);
  };

  // Sorting functionality
  const handleSort = (field: SortField) => {
    const newDirection = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc';
    setSortField(field);
    setSortDirection(newDirection);

    const sorted = [...filteredClientStrategies].sort((a, b) => {
      let aValue: any = a[field];
      let bValue: any = b[field];

      // Handle boolean fields
      if (field === 'is_enabled') {
        aValue = a.is_enabled ? 1 : 0;
        bValue = b.is_enabled ? 1 : 0;
      }

      // Handle date fields
      if (field === 'created_at') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // Handle string fields
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return newDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return newDirection === 'asc' ? 1 : -1;
      return 0;
    });

    setFilteredClientStrategies(sorted);
  };

  // Render sort icon
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />;
  };

  const handleAdd = () => {
    setIsEditing(false);
    setSelectedClientStrategy(null);
    form.reset();
    setIsDialogOpen(true);
  };

  const handleEdit = (clientStrategy: ClientStrategy) => {
    setIsEditing(true);
    setSelectedClientStrategy(clientStrategy);
    form.reset({
      client_id: clientStrategy.client_id,
      strategy_id: clientStrategy.strategy_id,
      variety: clientStrategy.variety,
      is_enabled: clientStrategy.is_enabled,
      preference_type: clientStrategy.preference_type,
      preferred_instrument: clientStrategy.preferred_instrument,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (clientStrategy: ClientStrategy) => {
    try {
      const response = await deleteClientStrategy(clientStrategy.client_id, clientStrategy.strategy_id);
      if (response.status === 'success') {
        toast({
          title: "Success",
          description: "Client strategy deleted successfully",
        });
        fetchData();
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to delete client strategy",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  const handleToggle = async (clientStrategy: ClientStrategy) => {
    try {
      const response = await updateClientStrategy(
        clientStrategy.client_id,
        clientStrategy.strategy_id,
        { is_enabled: !clientStrategy.is_enabled }
      );
      if (response.status === 'success') {
        toast({
          title: "Success",
          description: `Client strategy ${clientStrategy.is_enabled ? 'disabled' : 'enabled'} successfully`,
        });
        fetchData();
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to toggle client strategy",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  const handleViewRules = (clientStrategy: ClientStrategy) => {
    setSelectedClientStrategyForRules(clientStrategy);
    setIsRulesDialogOpen(true);
  };

  const onSubmit = async (values: ClientStrategyFormValues) => {
    try {
      const clientStrategyData: Omit<ClientStrategy, 'created_at' | 'updated_at'> = {
        client_id: values.client_id,
        strategy_id: values.strategy_id,
        variety: values.variety,
        is_enabled: values.is_enabled,
        preference_type: values.preference_type,
        preferred_instrument: values.preferred_instrument,
      };

      if (isEditing && selectedClientStrategy) {
        const response = await updateClientStrategy(
          selectedClientStrategy.client_id,
          selectedClientStrategy.strategy_id,
          values
        );
        if (response.status === 'success') {
          toast({
            title: "Success",
            description: "Client strategy updated successfully",
          });
        } else {
          toast({
            title: "Error",
            description: response.message || "Failed to update client strategy",
            variant: "destructive"
          });
        }
      } else {
        const response = await createClientStrategy(clientStrategyData);
        if (response.status === 'success') {
          toast({
            title: "Success",
            description: "Client strategy created successfully",
          });
        } else {
          toast({
            title: "Error",
            description: response.message || "Failed to create client strategy",
            variant: "destructive"
          });
        }
      }
      
      setIsDialogOpen(false);
      fetchData();
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  const filterFields = [
    { key: 'clientName', label: 'Client', type: 'text' as const },
    { key: 'strategyName', label: 'Strategy', type: 'text' as const },
    { key: 'variety', label: 'Variety', type: 'select' as const, options: [
      { value: 'NORMAL', label: 'NORMAL' },
      { value: 'ROBO', label: 'ROBO' },
    ]},
    { key: 'preference_type', label: 'Order Preference', type: 'select' as const, options: [
      { value: 'AS_RECEIVED', label: 'AS RECEIVED' },
      { value: 'FORCE_BUY', label: 'FORCE BUY' },
      { value: 'FORCE_SELL', label: 'FORCE SELL' },
    ]},
    { key: 'preferred_instrument', label: 'Instrument Preference', type: 'select' as const, options: [
      { value: 'ANY', label: 'ANY' },
      { value: 'EQ', label: 'EQUITY' },
      { value: 'FUTURE', label: 'FUTURE' },
      { value: 'CE', label: 'CALL OPTIONS' },
      { value: 'PE', label: 'PUT OPTIONS' },
    ]},
    { key: 'is_enabled', label: 'Status', type: 'select' as const, options: [
      { value: 'true', label: 'Enabled' },
      { value: 'false', label: 'Disabled' },
    ]}
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-pulse text-xl">Loading client strategies...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Users size={24} className="text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Client Strategies</h1>
            <p className="text-sm text-muted-foreground">Manage client strategy assignments and configurations</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={fetchData} disabled={loading}>
            <RefreshCw size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button onClick={handleAdd}>
            <Plus size={16} className="mr-2" /> Add Assignment
          </Button>
        </div>
      </div>

      <Card className="p-6">
        <div className="space-y-6">
          <div className="flex items-center space-x-2 pb-2 border-b">
            <Filter size={18} className="text-primary" />
            <h3 className="text-lg font-semibold">Filters & Search</h3>
          </div>

          <DataTableFilter
            fields={filterFields}
            onFilter={handleFilter}
            onReset={handleReset}
          />

          <div className="table-container">
            <table className="data-table">
              <thead>
                <tr>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('clientName')}
                  >
                    <div className="flex items-center space-x-2">
                      <Users size={16} className="text-muted-foreground" />
                      <span>CLIENT</span>
                      {renderSortIcon('clientName')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('strategyName')}
                  >
                    <div className="flex items-center space-x-2">
                      <Target size={16} className="text-muted-foreground" />
                      <span>STRATEGY</span>
                      {renderSortIcon('strategyName')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('variety')}
                  >
                    <div className="flex items-center space-x-2">
                      <Settings size={16} className="text-muted-foreground" />
                      <span>VARIETY</span>
                      {renderSortIcon('variety')}
                    </div>
                  </th>
                  <th>
                    <div className="flex items-center space-x-2">
                      <Target size={16} className="text-muted-foreground" />
                      <span>ORDER PREFERENCE</span>
                    </div>
                  </th>
                  <th>
                    <div className="flex items-center space-x-2">
                      <Settings size={16} className="text-muted-foreground" />
                      <span>INSTRUMENT PREFERENCE</span>
                    </div>
                  </th>
                  <th>
                    <div className="flex items-center space-x-2">
                      <Wrench size={16} className="text-muted-foreground" />
                      <span>RULES</span>
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('is_enabled')}
                  >
                    <div className="flex items-center space-x-2">
                      <ToggleLeft size={16} className="text-muted-foreground" />
                      <span>STATUS</span>
                      {renderSortIcon('is_enabled')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('created_at')}
                  >
                    <div className="flex items-center space-x-2">
                      <Calendar size={16} className="text-muted-foreground" />
                      <span>CREATED</span>
                      {renderSortIcon('created_at')}
                    </div>
                  </th>
                  <th>ACTIONS</th>
                </tr>
              </thead>
              <tbody>
                {filteredClientStrategies.map((cs, index) => (
                  <tr key={`${cs.client_id}-${cs.strategy_id}-${index}`}>
                    <td>{cs.clientName}</td>
                    <td>{cs.strategyName}</td>
                    <td>{cs.variety}</td>
                    <td>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        cs.preference_type === 'AS_RECEIVED' ? 'bg-blue-100 text-blue-800' :
                        cs.preference_type === 'FORCE_BUY' ? 'bg-green-100 text-green-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {cs.preference_type.replace('_', ' ')}
                      </span>
                    </td>
                    <td>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        cs.preferred_instrument === 'ANY' ? 'bg-gray-100 text-gray-800' :
                        cs.preferred_instrument === 'EQ' ? 'bg-yellow-100 text-yellow-800' :
                        cs.preferred_instrument === 'FUTURE' ? 'bg-blue-100 text-blue-800' :
                        cs.preferred_instrument === 'CE' ? 'bg-green-100 text-green-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {cs.preferred_instrument}
                      </span>
                    </td>
                    <td>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewRules(cs)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {cs.rule_count || 0} Rules
                      </Button>
                    </td>
                    <td>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={cs.is_enabled}
                          onCheckedChange={() => handleToggle(cs)}
                        />
                        <span className="text-sm text-muted-foreground">
                          {cs.is_enabled ? 'Enabled' : 'Disabled'}
                        </span>
                      </div>
                    </td>
                    <td>{formatTimestamp(cs.created_at)}</td>
                    <td>
                      <div className="flex space-x-1">
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="action-icon"
                          onClick={() => handleViewRules(cs)}
                        >
                          <Eye size={16} />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="action-icon"
                          onClick={() => handleEdit(cs)}
                        >
                          <Edit size={16} />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="action-icon"
                          onClick={() => handleDelete(cs)}
                        >
                          <Trash2 size={16} />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {filteredClientStrategies.length === 0 && (
              <div className="text-center p-6">
                <p className="text-muted-foreground">No client strategies found</p>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{isEditing ? 'Edit' : 'Add'} Client Strategy</DialogTitle>
            <DialogDescription>
              {isEditing ? 'Update the client strategy assignment.' : 'Assign a strategy to a client.'}
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="client_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Client</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isEditing}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select client" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {clients.map((client) => (
                          <SelectItem key={client.id} value={client.id}>
                            {client.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="strategy_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Strategy</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isEditing}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select strategy" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {strategies.map((strategy) => (
                          <SelectItem key={strategy.id} value={strategy.id}>
                            {strategy.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="variety"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Variety</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select variety" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="NORMAL">NORMAL</SelectItem>
                        <SelectItem value="ROBO">ROBO</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="preference_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Order Direction Preference</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select order preference" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="AS_RECEIVED">
                          <div className="flex flex-col">
                            <span>AS RECEIVED</span>
                            <span className="text-xs text-muted-foreground">Place orders exactly as received from strategy</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="FORCE_BUY">
                          <div className="flex flex-col">
                            <span>FORCE BUY</span>
                            <span className="text-xs text-muted-foreground">Always convert orders to BUY</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="FORCE_SELL">
                          <div className="flex flex-col">
                            <span>FORCE SELL</span>
                            <span className="text-xs text-muted-foreground">Always convert orders to SELL</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="preferred_instrument"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Instrument Type Preference</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select instrument preference" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="ANY">
                          <div className="flex flex-col">
                            <span>ANY</span>
                            <span className="text-xs text-muted-foreground">Use instrument type from strategy signal</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="EQ">
                          <div className="flex flex-col">
                            <span>EQUITY (EQ)</span>
                            <span className="text-xs text-muted-foreground">Always trade equity (removes strike and expiry)</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="FUTURE">
                          <div className="flex flex-col">
                            <span>FUTURE</span>
                            <span className="text-xs text-muted-foreground">Always trade futures (removes strike, keeps expiry)</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="CE">
                          <div className="flex flex-col">
                            <span>CALL OPTIONS (CE)</span>
                            <span className="text-xs text-muted-foreground">Always trade call options</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="PE">
                          <div className="flex flex-col">
                            <span>PUT OPTIONS (PE)</span>
                            <span className="text-xs text-muted-foreground">Always trade put options</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="is_enabled"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Enabled</FormLabel>
                      <div className="text-[0.8rem] text-muted-foreground">
                        Enable this strategy assignment for the client
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button variant="outline" type="button" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  {isEditing ? 'Update' : 'Create'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Client Strategy Rules Dialog */}
      {selectedClientStrategyForRules && (
        <ClientStrategyRulesDialog
          open={isRulesDialogOpen}
          onClose={() => setIsRulesDialogOpen(false)}
          clientId={selectedClientStrategyForRules.client_id}
          strategyId={selectedClientStrategyForRules.strategy_id}
          clientName={selectedClientStrategyForRules.clientName || 'Unknown Client'}
          strategyName={selectedClientStrategyForRules.strategyName || 'Unknown Strategy'}
        />
      )}
    </div>
  );
};

export default ClientStrategies;
