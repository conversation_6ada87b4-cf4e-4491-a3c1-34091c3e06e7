
import React, { useEffect, useState } from 'react';
import { useToast } from "@/hooks/use-toast";
import { getStrategies, createStrategy, updateStrategy, deleteStrategy } from '@/api/apiService';
import { Strategy } from '@/api/types';
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Edit, Eye, Plus, Trash2, RefreshCw, Filter, Target, FileText, Settings, ToggleLeft, Calendar } from 'lucide-react';
import DataTableFilter from '@/components/Filters/DataTableFilter';
import ViewStrategyDialog from '@/components/Dialogs/ViewStrategyDialog';
import AddEditStrategyDialog from '@/components/Dialogs/AddEditStrategyDialog';
import DeleteStrategyDialog from '@/components/Dialogs/DeleteStrategyDialog';
import { ApiConfig } from '@/api/config';

const Strategies: React.FC = () => {
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [filteredStrategies, setFilteredStrategies] = useState<Strategy[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isAddEditDialogOpen, setIsAddEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const { toast } = useToast();

  const fetchStrategies = async () => {
    try {
      setLoading(true);
      const response = await getStrategies();
      if (response.status === 'success') {
        setStrategies(response.data.data);
        setFilteredStrategies(response.data.data);
      } else {
        toast({
          title: "Error",
          description: "Failed to load strategies",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStrategies();
  }, [ApiConfig.useMockData]);

  const handleFilter = (filters: Record<string, string>) => {
    if (Object.values(filters).every(value => value === '')) {
      setFilteredStrategies(strategies);
      return;
    }

    const filtered = strategies.filter(strategy => {
      return Object.entries(filters).every(([key, value]) => {
        if (!value) return true;
        
        if (key === 'search') {
          const searchValue = value.toLowerCase();
          return (
            strategy.name.toLowerCase().includes(searchValue) ||
            strategy.description.toLowerCase().includes(searchValue) ||
            (strategy.is_active ? 'active' : 'inactive').toLowerCase().includes(searchValue)
          );
        }
        
        const strategyValue = (strategy as any)[key];
        return strategyValue && strategyValue.toLowerCase().includes(value.toLowerCase());
      });
    });
    
    setFilteredStrategies(filtered);
  };

  const handleReset = () => {
    setFilteredStrategies(strategies);
  };

  const handleViewStrategy = (strategy: Strategy) => {
    setSelectedStrategy(strategy);
    setIsViewDialogOpen(true);
  };

  const handleAddStrategy = () => {
    setSelectedStrategy(null);
    setIsEditing(false);
    setIsAddEditDialogOpen(true);
  };

  const handleEditStrategy = (strategy: Strategy) => {
    setSelectedStrategy(strategy);
    setIsEditing(true);
    setIsAddEditDialogOpen(true);
  };

  const handleDeleteClick = (strategy: Strategy) => {
    setSelectedStrategy(strategy);
    setIsDeleteDialogOpen(true);
  };

  const handleToggleStrategy = async (strategy: Strategy) => {
    try {
      const response = await updateStrategy(strategy.id, { 
        is_active: !strategy.is_active 
      });
      if (response.status === 'success') {
        toast({
          title: "Success",
          description: `Strategy ${strategy.is_active ? 'deactivated' : 'activated'} successfully`,
        });
        fetchStrategies();
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to toggle strategy",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  const handleCreateStrategy = async (formData: any) => {
    try {
      const response = await createStrategy(formData);
      if (response.status === 'success') {
        toast({
          title: "Success",
          description: "Strategy created successfully",
        });
        fetchStrategies();
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to create strategy",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsAddEditDialogOpen(false);
    }
  };

  const handleUpdateStrategy = async (formData: any) => {
    if (!selectedStrategy) return;
    
    try {
      const response = await updateStrategy(selectedStrategy.id, formData);
      if (response.status === 'success') {
        toast({
          title: "Success",
          description: "Strategy updated successfully",
        });
        fetchStrategies();
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to update strategy",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsAddEditDialogOpen(false);
    }
  };

  const handleDeleteStrategy = async () => {
    if (!selectedStrategy) return;
    
    try {
      const response = await deleteStrategy(selectedStrategy.id);
      if (response.status === 'success') {
        toast({
          title: "Success",
          description: "Strategy deleted successfully",
        });
        fetchStrategies();
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to delete strategy",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsDeleteDialogOpen(false);
    }
  };

  const handleSubmit = (formData: any) => {
    if (isEditing) {
      handleUpdateStrategy(formData);
    } else {
      handleCreateStrategy(formData);
    }
  };

  const filterFields = [
    { key: 'name', label: 'Name', type: 'text' as const },
    { key: 'description', label: 'Description', type: 'text' as const },
    { key: 'is_active', label: 'Status', type: 'select' as const, options: [
      { value: 'true', label: 'Active' },
      { value: 'false', label: 'Inactive' },
    ]},
    { key: 'created_at', label: 'Created Date', type: 'date' as const }
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-pulse text-xl">Loading strategies...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Target size={24} className="text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Trading Strategies</h1>
            <p className="text-sm text-muted-foreground">Manage and configure trading strategies</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={fetchStrategies} disabled={loading}>
            <RefreshCw size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button onClick={handleAddStrategy}>
            <Plus size={16} className="mr-1" /> Add New Strategy
          </Button>
        </div>
      </div>

      <Card>
        <div className="p-6">
          <div className="flex items-center space-x-2 pb-2 border-b mb-6">
            <Filter size={18} className="text-primary" />
            <h3 className="text-lg font-semibold">Filters & Search</h3>
          </div>

          <DataTableFilter
            fields={filterFields}
            onFilter={handleFilter}
            onReset={handleReset}
          />

          <div className="table-container">
            <table className="data-table">
              <thead>
                <tr>
                  <th>
                    <div className="flex items-center space-x-2">
                      <FileText size={16} className="text-muted-foreground" />
                      <span>NAME</span>
                    </div>
                  </th>
                  <th>
                    <div className="flex items-center space-x-2">
                      <FileText size={16} className="text-muted-foreground" />
                      <span>DESCRIPTION</span>
                    </div>
                  </th>
                  <th>
                    <div className="flex items-center space-x-2">
                      <Settings size={16} className="text-muted-foreground" />
                      <span>PARAMETERS</span>
                    </div>
                  </th>
                  <th>
                    <div className="flex items-center space-x-2">
                      <ToggleLeft size={16} className="text-muted-foreground" />
                      <span>STATUS</span>
                    </div>
                  </th>
                  <th>
                    <div className="flex items-center space-x-2">
                      <Calendar size={16} className="text-muted-foreground" />
                      <span>CREATED</span>
                    </div>
                  </th>
                  <th>ACTIONS</th>
                </tr>
              </thead>
              <tbody>
                {filteredStrategies.map((strategy) => (
                  <tr key={strategy.id}>
                    <td>{strategy.name}</td>
                    <td>{strategy.description}</td>
                    <td>{strategy.parameters}</td>
                    <td>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={strategy.is_active}
                          onCheckedChange={() => handleToggleStrategy(strategy)}
                        />
                        <span className="text-sm text-muted-foreground">
                          {strategy.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </td>
                    <td>{strategy.created_at}</td>
                    <td>
                      <div className="flex space-x-1">
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="action-icon"
                          onClick={() => handleViewStrategy(strategy)}
                        >
                          <Eye size={16} />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="action-icon"
                          onClick={() => handleEditStrategy(strategy)}
                        >
                          <Edit size={16} />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="action-icon"
                          onClick={() => handleDeleteClick(strategy)}
                        >
                          <Trash2 size={16} />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {filteredStrategies.length === 0 && (
              <div className="text-center p-6">
                <p className="text-muted-foreground">No strategies found</p>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Dialogs */}
      <ViewStrategyDialog 
        strategy={selectedStrategy}
        isOpen={isViewDialogOpen}
        onClose={() => setIsViewDialogOpen(false)}
      />
      
      <AddEditStrategyDialog
        strategy={isEditing ? selectedStrategy || undefined : undefined}
        isOpen={isAddEditDialogOpen}
        onClose={() => setIsAddEditDialogOpen(false)}
        onSubmit={handleSubmit}
      />
      
      <DeleteStrategyDialog
        strategyName={selectedStrategy?.name || ''}
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onDelete={handleDeleteStrategy}
      />
    </div>
  );
};

export default Strategies;
