
import React, { useEffect, useState } from 'react';
import { useToast } from "@/hooks/use-toast";
import { toast } from "sonner";
import { getClients, getClient, deleteClient, toggleClient } from '@/api/apiService';
import { Client } from '@/api/types';
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Eye, Edit, Trash2, Plus, RefreshCw, Filter, User, Mail, Building, CheckCircle, Calendar, ChevronUp, ChevronDown, FileText } from 'lucide-react';
import DataTableFilter from '@/components/Filters/DataTableFilter';
import AddClientDialog from '@/components/Dialogs/AddClientDialog';
import EditClientDialog from '@/components/Dialogs/EditClientDialog';
import ViewClientDialog from '@/components/Dialogs/ViewClientDialog';
import ApiToggleWrapper from '@/components/Layout/ApiToggleWrapper';
import BrokerIcon from '@/components/BrokerIcon';
import { ApiConfig } from '@/api/config';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import EnhancedJsonViewer from '@/components/EnhancedJsonViewer';

// Utility function to format timestamp (DD-MM-YYYY HH:MI AM/PM)
const formatTimestamp = (timestamp: string): string => {
  try {
    const date = new Date(timestamp);
    return date.toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).replace(',', '');
  } catch (e) {
    // If parsing fails, try to remove timezone info manually
    return timestamp.replace(/\s*GMT.*$/, '').replace(/\s*\+.*$/, '').replace(/T.*Z$/, '');
  }
};

type SortField = 'name' | 'email' | 'broker' | 'is_active' | 'created_at';
type SortDirection = 'asc' | 'desc';

const Clients: React.FC = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortField, setSortField] = useState<SortField>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isClientDetailsOpen, setIsClientDetailsOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [clientDetails, setClientDetails] = useState<Client | null>(null);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const { toast: toastNotification } = useToast();

  useEffect(() => {
    fetchClients();
  }, [ApiConfig.useMockData]);

  const fetchClients = async () => {
    setLoading(true);
    try {
      const response = await getClients();

      if (response.status === 'success') {
        setClients(response.data.data);
        setFilteredClients(response.data.data);
      } else {
        toastNotification({
          title: "Error",
          description: response.message || "Failed to load clients",
          variant: "destructive"
        });
      }
    } catch (error) {
      toastNotification({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFilter = (filters: Record<string, string>) => {
    if (Object.values(filters).every(value => value === '')) {
      setFilteredClients(clients);
      return;
    }

    const filtered = clients.filter(client => {
      return Object.entries(filters).every(([key, value]) => {
        if (!value) return true;
        
        if (key === 'search') {
          const searchValue = value.toLowerCase();
          return (
            client.name.toLowerCase().includes(searchValue) ||
            (client.email && client.email.toLowerCase().includes(searchValue)) ||
            client.broker.toLowerCase().includes(searchValue) ||
            (client.is_active ? "active" : "inactive").includes(searchValue)
          );
        }

        if (key === 'is_active') {
          return client.is_active === (value === 'true');
        }
        
        const clientValue = (client as any)[key];
        return clientValue && clientValue.toString().toLowerCase().includes(value.toLowerCase());
      });
    });
    
    setFilteredClients(filtered);
  };

  const handleReset = () => {
    setFilteredClients(clients);
  };

  // Sorting functionality
  const handleSort = (field: SortField) => {
    const newDirection = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc';
    setSortField(field);
    setSortDirection(newDirection);

    const sorted = [...filteredClients].sort((a, b) => {
      let aValue: any = a[field];
      let bValue: any = b[field];

      // Handle boolean fields
      if (field === 'is_active') {
        aValue = a.is_active ? 1 : 0;
        bValue = b.is_active ? 1 : 0;
      }

      // Handle date fields
      if (field === 'created_at') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // Handle string fields
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return newDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return newDirection === 'asc' ? 1 : -1;
      return 0;
    });

    setFilteredClients(sorted);
  };

  // Render sort icon
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />;
  };

  // Client details functionality
  const handleClientDetails = async (client: Client) => {
    setSelectedClient(client);
    setLoadingDetails(true);
    setIsClientDetailsOpen(true);

    try {
      const response = await getClient(client.id);
      if (response.status === 'success') {
        setClientDetails(response.data);
      } else {
        toastNotification({
          title: "Error",
          description: response.message || "Failed to load client details",
          variant: "destructive"
        });
      }
    } catch (error) {
      toastNotification({
        title: "Error",
        description: "An unexpected error occurred while loading client details",
        variant: "destructive"
      });
    } finally {
      setLoadingDetails(false);
    }
  };

  const handleAddClient = () => {
    setIsAddDialogOpen(true);
  };

  const handleViewClient = (client: Client) => {
    setSelectedClient(client);
    setIsViewDialogOpen(true);
  };

  const handleEditClient = (client: Client) => {
    setSelectedClient(client);
    setIsEditDialogOpen(true);
  };

  const handleDeleteClient = (client: Client) => {
    setSelectedClient(client);
    setIsDeleteDialogOpen(true);
  };

  const handleToggleClient = async (client: Client) => {
    try {
      const response = await toggleClient(client.id);
      if (response.status === 'success') {
        toast.success(`Client ${client.is_active ? 'deactivated' : 'activated'} successfully`);
        await fetchClients();
      } else {
        toast.error(response.message || "Failed to toggle client status");
      }
    } catch (error) {
      toast.error("An error occurred while toggling client status");
    }
  };

  const confirmDeleteClient = async () => {
    if (!selectedClient) return;
    
    try {
      const response = await deleteClient(selectedClient.id);
      if (response.status === 'success') {
        toast.success("Client deleted successfully");
        setIsDeleteDialogOpen(false);
        setSelectedClient(null);
        fetchClients();
      } else {
        toast.error(response.message || "Failed to delete client");
      }
    } catch (error) {
      toast.error("An error occurred while deleting the client");
    }
  };

  const filterFields = [
    { key: 'name', label: 'Name', type: 'text' as const },
    { key: 'email', label: 'Email', type: 'text' as const },
    { key: 'broker', label: 'Broker', type: 'select' as const, options: [
      { value: 'zerodha', label: 'Zerodha' },
      { value: 'angel_one', label: 'Angel One' },
      { value: 'fyers', label: 'Fyers' },
      { value: 'aliceblue', label: 'Alice Blue' },
      { value: 'upstox', label: 'Upstox' },
      { value: 'finvasia', label: 'Finvasia' },
      { value: 'fyers_enterprise', label: 'Fyers Enterprise' },
      { value: 'flattrade', label: 'Flattrade' },
    ]},
    { key: 'is_active', label: 'Status', type: 'select' as const, options: [
      { value: 'true', label: 'Active' },
      { value: 'false', label: 'Inactive' },
    ]},
    { key: 'created_at', label: 'Created Date', type: 'date' as const }
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-pulse text-xl">Loading clients...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <User size={24} className="text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Clients</h1>
            <p className="text-sm text-muted-foreground">Manage client accounts and broker configurations</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={fetchClients} disabled={loading}>
            <RefreshCw size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button onClick={handleAddClient}>
            <Plus size={16} className="mr-1" /> Add Client
          </Button>
        </div>
      </div>

      <ApiToggleWrapper />

      <Card>
        <div className="p-6">
          <div className="flex items-center space-x-2 pb-2 border-b mb-6">
            <Filter size={18} className="text-primary" />
            <h3 className="text-lg font-semibold">Filters & Search</h3>
          </div>

          <DataTableFilter
            fields={filterFields}
            onFilter={handleFilter}
            onReset={handleReset}
          />

          <div className="table-container">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('name')}
                  >
                    <div className="flex items-center space-x-2">
                      <User size={16} className="text-muted-foreground" />
                      <span>NAME</span>
                      {renderSortIcon('name')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('email')}
                  >
                    <div className="flex items-center space-x-2">
                      <Mail size={16} className="text-muted-foreground" />
                      <span>EMAIL</span>
                      {renderSortIcon('email')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('broker')}
                  >
                    <div className="flex items-center space-x-2">
                      <Building size={16} className="text-muted-foreground" />
                      <span>BROKER</span>
                      {renderSortIcon('broker')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('is_active')}
                  >
                    <div className="flex items-center space-x-2">
                      <CheckCircle size={16} className="text-muted-foreground" />
                      <span>STATUS</span>
                      {renderSortIcon('is_active')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('created_at')}
                  >
                    <div className="flex items-center space-x-2">
                      <Calendar size={16} className="text-muted-foreground" />
                      <span>CREATED</span>
                      {renderSortIcon('created_at')}
                    </div>
                  </TableHead>
                  <TableHead>ACTIONS</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredClients.map((client) => (
                  <TableRow key={client.id}>
                    <TableCell>{client.name}</TableCell>
                    <TableCell>{client.email || 'Not set'}</TableCell>
                    <TableCell>
                      <BrokerIcon
                        broker={client.broker}
                        variant="chip"
                        showLabel={true}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={client.is_active}
                          onCheckedChange={() => handleToggleClient(client)}
                        />
                        <span className="text-sm text-muted-foreground">
                          {client.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{formatTimestamp(client.created_at)}</TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="action-icon"
                          onClick={() => handleViewClient(client)}
                          title="View Client"
                        >
                          <Eye size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="action-icon"
                          onClick={() => handleClientDetails(client)}
                          title="Client Details (JSON)"
                        >
                          <FileText size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="action-icon"
                          onClick={() => handleEditClient(client)}
                          title="Edit Client"
                        >
                          <Edit size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="action-icon"
                          onClick={() => handleDeleteClient(client)}
                          title="Delete Client"
                        >
                          <Trash2 size={16} />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            
            {filteredClients.length === 0 && (
              <div className="text-center p-6">
                <p className="text-muted-foreground">No clients found</p>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Add Client Dialog */}
      <AddClientDialog 
        isOpen={isAddDialogOpen} 
        onClose={() => setIsAddDialogOpen(false)}
        onSuccess={() => {
          setIsAddDialogOpen(false);
          fetchClients();
        }}
      />

      {/* Edit Client Dialog */}
      {selectedClient && (
        <EditClientDialog 
          isOpen={isEditDialogOpen} 
          onClose={() => setIsEditDialogOpen(false)}
          onSuccess={() => {
            setIsEditDialogOpen(false);
            fetchClients();
          }}
          client={selectedClient}
        />
      )}

      {/* View Client Dialog */}
      {selectedClient && (
        <ViewClientDialog 
          isOpen={isViewDialogOpen} 
          onClose={() => setIsViewDialogOpen(false)}
          client={selectedClient}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Client</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedClient?.name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteClient}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Client Details Dialog */}
      <Dialog open={isClientDetailsOpen} onOpenChange={setIsClientDetailsOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <FileText size={20} />
              <span>Client Details - {selectedClient?.name}</span>
            </DialogTitle>
            <DialogDescription>
              Complete client information and configuration details
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-auto">
            {loadingDetails ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-pulse text-lg">Loading client details...</div>
              </div>
            ) : clientDetails ? (
              <EnhancedJsonViewer
                data={JSON.stringify(clientDetails, null, 2)}
                maxHeight="500px"
                showCopyButton={true}
                showViewFullButton={true}
                title="Client Details"
              />
            ) : (
              <div className="text-center p-8 text-muted-foreground">
                No client details available
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsClientDetailsOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Clients;
