
import React, { useEffect, useState } from 'react';
import { useToast } from "@/hooks/use-toast";
import { getOrders } from '@/api/apiService';
import { Order } from '@/api/types';
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { RefreshCw, Filter, ShoppingCart, ChevronUp, ChevronDown, Hash, Users, Target, Settings, TrendingUp, DollarSign, Calendar, Package, IndianRupee, Building, CheckCircle, ArrowUpDown } from 'lucide-react';
import DataTableFilter from '@/components/Filters/DataTableFilter';
import { ApiConfig } from '@/api/config';
import { getOrderTypeChipConfig, getStatusChipConfig, getExpiryTypeChipConfig } from '@/utils/orderTypeUtils';

// Utility function to format timestamp (DD-MM-YYYY HH:MI AM/PM)
const formatTimestamp = (timestamp: string): string => {
  try {
    const date = new Date(timestamp);
    return date.toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).replace(',', '');
  } catch (e) {
    // If parsing fails, try to remove timezone info manually
    return timestamp.replace(/\s*GMT.*$/, '').replace(/\s*\+.*$/, '').replace(/T.*Z$/, '');
  }
};

// Utility function to format strike as integer
const formatStrike = (strike: any): string => {
  if (!strike || strike === null || strike === undefined) return '-';
  const strikeNum = parseFloat(strike);
  return isNaN(strikeNum) ? '-' : Math.round(strikeNum).toString();
};

// Utility function to format price with 2 decimal places
const formatPrice = (price: any): string => {
  if (!price || price === null || price === undefined) return '-';
  const priceNum = parseFloat(price);
  return isNaN(priceNum) ? '-' : priceNum.toFixed(2);
};

// Utility function to truncate ID to 5 characters
const truncateId = (id: any): string => {
  if (!id) return '-';
  return id.toString().substring(0, 5);
};

// Function to format expiry date as DD-MMM-YY
const formatExpiryDate = (dateStr: string): string => {
  if (!dateStr) return '-';
  
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr; // Return original if invalid date
    
    const day = String(date.getDate()).padStart(2, '0');
    const month = date.toLocaleString('default', { month: 'short' }).toUpperCase();
    const year = String(date.getFullYear()).slice(-2);
    
    return `${day}-${month}-${year}`;
  } catch (e) {
    return dateStr; // Return original if parsing fails
  }
};

type SortField = 'id' | 'created' | 'clientName' | 'strategyName' | 'variety' | 'symbol' | 'strike' | 'expiry_type' | 'expirydate' | 'price' | 'qty' | 'target_price' | 'sl_price' | 'status' | 'order_type' | 'broker_order_id';
type SortDirection = 'asc' | 'desc';

const Orders: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortField, setSortField] = useState<SortField>('created');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [filterParams, setFilterParams] = useState({
    client_id: '',
    strategy_id: '',
    status: '',
    type: '',
    from_date: '',
    to_date: '',
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchOrders();
  }, [ApiConfig.useMockData]); // Re-fetch when API toggle changes

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await getOrders({
        page: 1,
        limit: 100,
        ...filterParams
      });
      
      if (response.status === 'success') {
        const formattedOrders = response.data.data.map((order: any) => ({
          ...order,
          clientName: order.client_name || order.clientName,
          strategyName: order.strategy_name || order.strategyName,
          expiryDate: order.expiry_date || order.expiryDate,
          expiryType: order.expiry_type || order.expiryType,
        }));
        setOrders(formattedOrders);
        setFilteredOrders(formattedOrders);
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to load orders",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFilter = (filters: Record<string, string>) => {
    if (Object.values(filters).every(value => value === '')) {
      setFilteredOrders(orders);
      return;
    }

    // Update filter parameters for API calls
    const apiFilterParams = {
      client_id: filters.clientName || '',
      strategy_id: filters.strategyName || '',
      status: filters.status || '',
      type: filters.type || '',
      from_date: filters.from_date || '',
      to_date: filters.to_date || '',
    };
    setFilterParams(apiFilterParams);

    // For immediate client-side filtering
    const filtered = orders.filter(order => {
      return Object.entries(filters).every(([key, value]) => {
        if (!value) return true;
        
        if (key === 'search') {
          const searchValue = value.toLowerCase();
          return (
            order.id.toString().toLowerCase().includes(searchValue) ||
            order.clientName.toLowerCase().includes(searchValue) ||
            order.strategyName.toLowerCase().includes(searchValue) ||
            order.symbol.toLowerCase().includes(searchValue) ||
            order.variety.toLowerCase().includes(searchValue) ||
            (order.broker_order_id && order.broker_order_id.toLowerCase().includes(searchValue)) ||
            (order.order_type && order.order_type.toLowerCase().includes(searchValue)) ||
            order.status.toLowerCase().includes(searchValue)
          );
        }
        
        const orderValue = (order as any)[key];
        if (!orderValue) return false;
        return orderValue.toString().toLowerCase().includes(value.toLowerCase());
      });
    });
    
    setFilteredOrders(filtered);
  };

  const handleReset = () => {
    setFilterParams({
      client_id: '',
      strategy_id: '',
      status: '',
      type: '',
      from_date: '',
      to_date: '',
    });
    setFilteredOrders(orders);
  };

  // Sorting functionality
  const handleSort = (field: SortField) => {
    const newDirection = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc';
    setSortField(field);
    setSortDirection(newDirection);

    const sorted = [...filteredOrders].sort((a, b) => {
      let aValue: any = a[field];
      let bValue: any = b[field];

      // Handle numeric fields
      if (field === 'strike' || field === 'price' || field === 'qty' || field === 'target_price' || field === 'sl_price') {
        aValue = parseFloat(aValue) || 0;
        bValue = parseFloat(bValue) || 0;
      }

      // Handle date fields
      if (field === 'created' || field === 'expirydate') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // Handle string fields
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return newDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return newDirection === 'asc' ? 1 : -1;
      return 0;
    });

    setFilteredOrders(sorted);
  };

  // Render sort icon
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />;
  };

  const handleApplyFilters = () => {
    fetchOrders();
  };

  const filterFields = [
    { key: 'from_date', label: 'From Date', type: 'date' as const },
    { key: 'to_date', label: 'To Date', type: 'date' as const },
    { key: 'id', label: 'Order ID', type: 'text' as const },
    { key: 'clientName', label: 'Client', type: 'text' as const },
    { key: 'strategyName', label: 'Strategy', type: 'text' as const },
    { key: 'symbol', label: 'Symbol', type: 'text' as const },
    { key: 'variety', label: 'Variety', type: 'text' as const },
    { key: 'strike', label: 'Strike', type: 'text' as const },
    { key: 'expirydate', label: 'Expiry Date', type: 'date' as const },
    { key: 'broker_order_id', label: 'Broker Order ID', type: 'text' as const },
    { key: 'order_type', label: 'Order Type', type: 'select' as const, options: [
      { value: 'BUY', label: 'BUY' },
      { value: 'SELL', label: 'SELL' },
    ]},
    { key: 'status', label: 'Status', type: 'select' as const, options: [
      { value: 'COMPLETE', label: 'COMPLETE' },
      { value: 'PENDING', label: 'PENDING' },
      { value: 'FAILED', label: 'FAILED' },
    ]}
  ];

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-pulse text-xl">Loading orders...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <ShoppingCart size={24} className="text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Order History</h1>
            <p className="text-sm text-muted-foreground">Track and manage all trading orders</p>
          </div>
        </div>
        <Button variant="outline" onClick={fetchOrders} disabled={loading}>
          <RefreshCw size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
          {loading ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      <Card>
        <div className="p-6">
          <div className="flex items-center space-x-2 pb-2 border-b mb-6">
            <Filter size={18} className="text-primary" />
            <h3 className="text-lg font-semibold">Filters & Search</h3>
          </div>

          <div className="flex flex-col space-y-4">
            <DataTableFilter
              fields={filterFields}
              onFilter={handleFilter}
              onReset={handleReset}
            />

            <div className="flex justify-end">
              <Button
                variant="outline"
                onClick={handleApplyFilters}
                className="flex items-center"
              >
                <Filter size={16} className="mr-2" />
                Apply Filters
              </Button>
            </div>
          </div>

          <div className="table-container mt-4">
            <table className="data-table">
              <thead>
                <tr>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('id')}
                  >
                    <div className="flex items-center space-x-2">
                      <Hash size={16} className="text-muted-foreground" />
                      <span>ID</span>
                      {renderSortIcon('id')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('created')}
                  >
                    <div className="flex items-center space-x-2">
                      <Calendar size={16} className="text-muted-foreground" />
                      <span>CREATED</span>
                      {renderSortIcon('created')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('clientName')}
                  >
                    <div className="flex items-center space-x-2">
                      <Users size={16} className="text-muted-foreground" />
                      <span>CLIENT</span>
                      {renderSortIcon('clientName')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('strategyName')}
                  >
                    <div className="flex items-center space-x-2">
                      <Target size={16} className="text-muted-foreground" />
                      <span>STRATEGY</span>
                      {renderSortIcon('strategyName')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('variety')}
                  >
                    <div className="flex items-center space-x-2">
                      <Settings size={16} className="text-muted-foreground" />
                      <span>VARIETY</span>
                      {renderSortIcon('variety')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('symbol')}
                  >
                    <div className="flex items-center space-x-2">
                      <TrendingUp size={16} className="text-muted-foreground" />
                      <span>SYMBOL</span>
                      {renderSortIcon('symbol')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('strike')}
                  >
                    <div className="flex items-center space-x-2">
                      <DollarSign size={16} className="text-muted-foreground" />
                      <span>STRIKE</span>
                      {renderSortIcon('strike')}
                    </div>
                  </th>
                  <th>
                    <div className="flex items-center space-x-2">
                      <Settings size={16} className="text-muted-foreground" />
                      <span>EXPIRY TYPE</span>
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('expirydate')}
                  >
                    <div className="flex items-center space-x-2">
                      <Calendar size={16} className="text-muted-foreground" />
                      <span>EXPIRY DATE</span>
                      {renderSortIcon('expirydate')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('price')}
                  >
                    <div className="flex items-center space-x-2">
                      <IndianRupee size={16} className="text-muted-foreground" />
                      <span>PRICE</span>
                      {renderSortIcon('price')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('qty')}
                  >
                    <div className="flex items-center space-x-2">
                      <Package size={16} className="text-muted-foreground" />
                      <span>QUANTITY</span>
                      {renderSortIcon('qty')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('target_price')}
                  >
                    <div className="flex items-center space-x-2">
                      <IndianRupee size={16} className="text-muted-foreground" />
                      <span>TARGET PRICE</span>
                      {renderSortIcon('target_price')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('stoploss_price')}
                  >
                    <div className="flex items-center space-x-2">
                      <IndianRupee size={16} className="text-muted-foreground" />
                      <span>SL PRICE</span>
                      {renderSortIcon('stoploss_price')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('status')}
                  >
                    <div className="flex items-center space-x-2">
                      <CheckCircle size={16} className="text-muted-foreground" />
                      <span>STATUS</span>
                      {renderSortIcon('status')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('order_type')}
                  >
                    <div className="flex items-center space-x-2">
                      <ArrowUpDown size={16} className="text-muted-foreground" />
                      <span>ORDER TYPE</span>
                      {renderSortIcon('order_type')}
                    </div>
                  </th>
                  <th
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('broker_order_id')}
                  >
                    <div className="flex items-center space-x-2">
                      <Building size={16} className="text-muted-foreground" />
                      <span>BROKER ORDER ID</span>
                      {renderSortIcon('broker_order_id')}
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredOrders.map((order) => (
                  <tr key={order.id}>
                    <td className="font-mono text-sm">{truncateId(order.id)}</td>
                    <td>{formatTimestamp(order.created || order.created_at)}</td>
                    <td>{order.clientName}</td>
                    <td>{order.strategyName}</td>
                    <td>{order.variety}</td>
                    <td>{order.symbol}</td>
                    <td className="text-center font-mono">{formatStrike(order.strike)}</td>
                    <td>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getExpiryTypeChipConfig(order.expiry_type).className}`}>
                        {order.expiry_type}
                      </span>
                    </td>
                    <td className="whitespace-nowrap">{formatExpiryDate(order.expirydate)}</td>
                    <td className="text-right">₹{order.price}</td>
                    <td className="text-center">{order.qty || order.quantity}</td>
                    <td className="text-right">₹{formatPrice(order.target_price)}</td>
                    <td className="text-right">₹{formatPrice(order.stoploss_price)}</td>
                    <td>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusChipConfig(order.status).className}`}>
                        {order.status}
                      </span>
                    </td>
                    <td>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getOrderTypeChipConfig(order.order_type, order.expiry_type).className}`}>
                        {order.order_type}
                      </span>
                    </td>
                    <td className="font-mono text-sm">{order.broker_order_id || '-'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {filteredOrders.length === 0 && (
              <div className="text-center p-6">
                <p className="text-muted-foreground">No orders found</p>
              </div>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
};

export default Orders;
