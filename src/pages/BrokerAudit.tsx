
import React, { useState, useEffect } from 'react';
import { useToast } from "@/hooks/use-toast";
import { getBrokerAuditTrail } from '@/api/apiService';
import { BrokerAuditTrail } from '@/api/types';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RefreshCw, Filter, ChevronUp, ChevronDown } from 'lucide-react';
import DataTableFilter from '@/components/Filters/DataTableFilter';
import { ApiConfig } from '@/api/config';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Enhanced<PERSON>sonViewer from '@/components/EnhancedJsonViewer';
import BrokerIcon from '@/components/BrokerIcon';
import { Clock, Hash, User, Target, Building, FileText, MessageSquare, CheckCircle, AlertCircle, Calendar, Package, IndianRupee } from 'lucide-react';

// Utility function to format timestamp with date and time on separate lines
const formatTimestamp = (timestamp: string): React.ReactNode => {
  try {
    const date = new Date(timestamp);
    const dateStr = date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
    const timeStr = date.toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
    
    return (
      <div className="flex flex-col whitespace-nowrap">
        <span>{dateStr}</span>
        <span className="text-xs text-muted-foreground">{timeStr}</span>
      </div>
    );
  } catch (e) {
    // Fallback to original format if parsing fails
    return timestamp.replace(/\s*GMT.*$/, '').replace(/\s*\+.*$/, '').replace(/T.*Z$/, '');
  }
};

// Function to get transaction type chip styling
const getTransactionTypeChip = (type: string) => {
  const typeUpper = String(type).toUpperCase();
  const isBuy = typeUpper === 'BUY' || typeUpper === 'BUY_MARKET' || typeUpper === 'BUY_LIMIT';
  const isSell = typeUpper === 'SELL' || typeUpper === 'SELL_MARKET' || typeUpper === 'SELL_LIMIT';
  
  if (isBuy) {
    return 'bg-green-500 text-white';
  } else if (isSell) {
    return 'bg-red-500 text-white';
  }
  return 'bg-gray-200 text-gray-800';
};

// Function to format expiry date as DD-MMM-YY
const formatExpiryDate = (dateStr: string): string => {
  if (!dateStr) return '-';
  
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr; // Return original if invalid date
    
    const day = String(date.getDate()).padStart(2, '0');
    const month = date.toLocaleString('default', { month: 'short' }).toUpperCase();
    const year = String(date.getFullYear()).slice(-2);
    
    return `${day}-${month}-${year}`;
  } catch (e) {
    return dateStr; // Return original if parsing fails
  }
};

type SortField = 'timestamp' | 'audit_id' | 'client_name' | 'strategy_name' | 'broker' | 'status' | 'symbol' | 'strike' | 'expiry_type' | 'expirydate' | 'transaction_type' | 'quantity' | 'price';
type SortDirection = 'asc' | 'desc';

interface BrokerRequestData {
  symbol?: string;
  strike?: string | number;
  expiry_type?: string;
  expirydate?: string;
  order_type?: string;
  transaction_type?: string;
  quantity?: number;
  price?: number;
  [key: string]: any; // For any other properties that might exist
}

const BrokerAuditPage: React.FC = () => {
  const [auditTrails, setAuditTrails] = useState<BrokerAuditTrail[]>([]);
  const [filteredAuditTrails, setFilteredAuditTrails] = useState<BrokerAuditTrail[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortField, setSortField] = useState<SortField>('timestamp');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [filterParams, setFilterParams] = useState({
    client_id: '',
    strategy_id: '',
    status: '',
    from_date: '',
    to_date: '',
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchAuditTrail();
  }, [ApiConfig.useMockData]);

  const fetchAuditTrail = async () => {
    try {
      setLoading(true);
      const response = await getBrokerAuditTrail({
        page: 1,
        limit: 100,
        ...filterParams
      });
      
      if (response.status === 'success') {
        const data = response.data.data;
        setAuditTrails(data);

        // Apply initial sorting
        const sorted = [...data].sort((a, b) => {
          let aValue: any = a[sortField];
          let bValue: any = b[sortField];

          if (sortField === 'timestamp') {
            aValue = new Date(aValue).getTime();
            bValue = new Date(bValue).getTime();
          }

          if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
          if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
          return 0;
        });

        setFilteredAuditTrails(sorted);
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to load broker audit trail data",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFilter = (filters: Record<string, string>) => {
    if (Object.values(filters).every(value => value === '')) {
      setFilteredAuditTrails(auditTrails);
      return;
    }

    const apiFilterParams = {
      client_id: filters.client_name || '',
      strategy_id: filters.strategy_name || '',
      status: filters.status || '',
      from_date: filters.from_date || '',
      to_date: filters.to_date || '',
    };
    setFilterParams(apiFilterParams);

    const filtered = auditTrails.filter(trail => {
      return Object.entries(filters).every(([key, value]) => {
        if (!value) return true;

        if (key === 'search') {
          const searchValue = value.toLowerCase();
          return (
            trail.client_name.toLowerCase().includes(searchValue) ||
            trail.strategy_name.toLowerCase().includes(searchValue) ||
            trail.status.toLowerCase().includes(searchValue) ||
            trail.broker.toLowerCase().includes(searchValue) ||
            trail.audit_id.toLowerCase().includes(searchValue)
          );
        }

        if (key === 'audit_id') {
          return trail.audit_id.toLowerCase().includes(value.toLowerCase());
        }

        if (key === 'client_name') {
          return trail.client_name.toLowerCase().includes(value.toLowerCase());
        }

        if (key === 'strategy_name') {
          return trail.strategy_name.toLowerCase().includes(value.toLowerCase());
        }

        if (key === 'broker') {
          return trail.broker.toLowerCase() === value.toLowerCase();
        }

        if (key === 'status') {
          return trail.status.toLowerCase() === value.toLowerCase();
        }

        if (key === 'from_date') {
          const trailDate = new Date(trail.timestamp);
          const filterDate = new Date(value);
          return trailDate >= filterDate;
        }

        if (key === 'to_date') {
          const trailDate = new Date(trail.timestamp);
          const filterDate = new Date(value);
          return trailDate <= filterDate;
        }

        return true;
      });
    });
    
    setFilteredAuditTrails(filtered);
  };

  const handleReset = () => {
    setFilterParams({
      client_id: '',
      strategy_id: '',
      status: '',
      from_date: '',
      to_date: '',
    });
    setFilteredAuditTrails(auditTrails);
  };

  // Sorting functionality
  const handleSort = (field: SortField) => {
    const newDirection = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc';
    setSortField(field);
    setSortDirection(newDirection);

    const sorted = [...filteredAuditTrails].sort((a, b) => {
      let aValue: any = a[field];
      let bValue: any = b[field];

      // Handle timestamp sorting
      if (field === 'timestamp') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (aValue < bValue) return newDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return newDirection === 'asc' ? 1 : -1;
      return 0;
    });

    setFilteredAuditTrails(sorted);
  };

  // Render sort icon
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />;
  };

  const filterFields = [
    { key: 'from_date', label: 'From Date', type: 'date' as const },
    { key: 'to_date', label: 'To Date', type: 'date' as const },
    { key: 'audit_id', label: 'Audit ID', type: 'text' as const },
    { key: 'client_name', label: 'Client Name', type: 'text' as const },
    { key: 'strategy_name', label: 'Strategy Name', type: 'text' as const },
    { key: 'broker', label: 'Broker', type: 'select' as const, options: [
      { value: 'zerodha', label: 'Zerodha' },
      { value: 'angel_one', label: 'Angel One' },
      { value: 'fyers', label: 'Fyers' },
      { value: 'upstox', label: 'Upstox' },
      { value: 'aliceblue', label: 'Alice Blue' },
    ]},
    { key: 'status', label: 'Status', type: 'select' as const, options: [
      { value: 'SUCCESS', label: 'SUCCESS' },
      { value: 'FAILED', label: 'FAILED' },
    ]}
  ];

  const handleApplyFilters = () => {
    fetchAuditTrail();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-pulse text-xl">Loading broker audit trail...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Building size={24} className="text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Broker Audit Trail</h1>
            <p className="text-sm text-muted-foreground">Monitor and track all broker interactions</p>
          </div>
        </div>
        <Button variant="outline" onClick={fetchAuditTrail} disabled={loading}>
          <RefreshCw size={16} className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
          {loading ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      <Card className="p-6">
        <div className="space-y-6">
          <div className="flex flex-col space-y-4">
            <div className="flex items-center space-x-2 pb-2 border-b">
              <Filter size={18} className="text-primary" />
              <h3 className="text-lg font-semibold">Filters & Search</h3>
            </div>

            <DataTableFilter
              fields={filterFields}
              onFilter={handleFilter}
              onReset={handleReset}
            />

            <div className="flex justify-end">
              <Button
                variant="outline"
                onClick={handleApplyFilters}
                className="flex items-center"
              >
                <Filter size={16} className="mr-2" />
                Apply Filters
              </Button>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('timestamp')}
                  >
                    <div className="flex items-center space-x-2">
                      <Clock size={16} className="text-muted-foreground" />
                      <span>Timestamp</span>
                      {renderSortIcon('timestamp')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none min-w-[100px]"
                    onClick={() => handleSort('audit_id')}
                  >
                    <div className="flex items-center space-x-2">
                      <Hash size={16} className="text-muted-foreground" />
                      <span>Audit ID</span>
                      {renderSortIcon('audit_id')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('client_name')}
                  >
                    <div className="flex items-center space-x-2">
                      <User size={16} className="text-muted-foreground" />
                      <span>Client</span>
                      {renderSortIcon('client_name')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('strategy_name')}
                  >
                    <div className="flex items-center space-x-2">
                      <Target size={16} className="text-muted-foreground" />
                      <span>Strategy</span>
                      {renderSortIcon('strategy_name')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('symbol')}
                  >
                    <div className="flex items-center space-x-2">
                      <Target size={16} className="text-muted-foreground" />
                      <span>Symbol</span>
                      {renderSortIcon('symbol')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('strike')}
                  >
                    <div className="flex items-center space-x-2">
                      <Target size={16} className="text-muted-foreground" />
                      <span>Strike</span>
                      {renderSortIcon('strike')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('expiry_type')}
                  >
                    <div className="flex items-center space-x-2">
                      <Calendar size={16} className="text-muted-foreground" />
                      <span>Expiry Type</span>
                      {renderSortIcon('expiry_type')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('expirydate')}
                  >
                    <div className="flex items-center space-x-2">
                      <Calendar size={16} className="text-muted-foreground" />
                      <span>Expiry Date</span>
                      {renderSortIcon('expirydate')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('transaction_type')}
                  >
                    <div className="flex items-center space-x-2">
                      <Target size={16} className="text-muted-foreground" />
                      <span>Type</span>
                      {renderSortIcon('transaction_type')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('quantity')}
                  >
                    <div className="flex items-center space-x-2">
                      <Package size={16} className="text-muted-foreground" />
                      <span>Qty</span>
                      {renderSortIcon('quantity')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('price')}
                  >
                    <div className="flex items-center space-x-2">
                      <IndianRupee size={16} className="text-muted-foreground" />
                      <span>Price</span>
                      {renderSortIcon('price')}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('broker')}
                  >
                    <div className="flex items-center space-x-2">
                      <Building size={16} className="text-muted-foreground" />
                      <span>Broker</span>
                      {renderSortIcon('broker')}
                    </div>
                  </TableHead>
                  <TableHead>
                    <div className="flex items-center space-x-2">
                      <FileText size={16} className="text-muted-foreground" />
                      <span>Request</span>
                    </div>
                  </TableHead>
                  <TableHead>
                    <div className="flex items-center space-x-2">
                      <MessageSquare size={16} className="text-muted-foreground" />
                      <span>Response</span>
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50 select-none"
                    onClick={() => handleSort('status')}
                  >
                    <div className="flex items-center space-x-2">
                      <CheckCircle size={16} className="text-muted-foreground" />
                      <span>Status</span>
                      {renderSortIcon('status')}
                    </div>
                  </TableHead>
                  <TableHead>
                    <div className="flex items-center space-x-2">
                      <AlertCircle size={16} className="text-muted-foreground" />
                      <span>Error</span>
                    </div>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAuditTrails.length > 0 ? (
                  filteredAuditTrails.map((trail) => {
                    let requestData: BrokerRequestData = {};
                    try {
                      requestData = JSON.parse(trail.broker_request) as BrokerRequestData;
                    } catch (e) {
                      console.error('Error parsing broker request:', e);
                    }
                    
                    return (
                      <TableRow key={trail.audit_id}>
                        <TableCell className="font-medium">{formatTimestamp(trail.timestamp)}</TableCell>
                        <TableCell className="font-mono text-xs min-w-[100px] relative group">
                          <span className="truncate block w-[70px]">{trail.audit_id.substring(0, 5)}...</span>
                          <div className="absolute left-0 top-full z-10 mt-1 p-2 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none">
                            {trail.audit_id}
                          </div>
                        </TableCell>
                        <TableCell>{trail.client_name}</TableCell>
                        <TableCell>{trail.strategy_name}</TableCell>
                        <TableCell>{requestData.original_symbol || '-'}</TableCell>
                        <TableCell>{requestData.strike || '-'}</TableCell>
                        <TableCell>{requestData.expiry_type || '-'}</TableCell>
                        <TableCell className="whitespace-nowrap">{formatExpiryDate(requestData.expirydate)}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTransactionTypeChip(requestData.transaction_type)}`}>
                            {requestData.transaction_type || '-'}
                          </span>
                        </TableCell>
                        <TableCell>{requestData.quantity || '-'}</TableCell>
                        <TableCell>{requestData.price ? `₹${requestData.price}` : '-'}</TableCell>
                        <TableCell>
                          <BrokerIcon
                            broker={trail.broker}
                            variant="chip"
                            showLabel={true}
                          />
                        </TableCell>
                        <TableCell className="max-w-[200px] overflow-hidden">
                          <EnhancedJsonViewer
                            data={trail.broker_request}
                            maxHeight="120px"
                            showCopyButton={false}
                            showViewFullButton={true}
                            title="Broker Request"
                          />
                        </TableCell>
                        <TableCell className="max-w-[200px] overflow-hidden">
                          <EnhancedJsonViewer
                            data={trail.broker_response}
                            maxHeight="120px"
                            showCopyButton={false}
                            showViewFullButton={true}
                            title="Broker Response"
                          />
                        </TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            trail.status === 'SUCCESS' ? 'bg-accent text-accent-foreground' : 'bg-destructive text-destructive-foreground'
                          }`}>
                            {trail.status}
                          </span>
                        </TableCell>
                        <TableCell className={`${trail.status === 'SUCCESS' ? 'text-green-600' : 'text-destructive'}`}>
                          {trail.error_message || '-'}
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={16} className="h-24 text-center">
                      No audit trail records found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default BrokerAuditPage;
