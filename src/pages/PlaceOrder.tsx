import React, { useState, useEffect } from 'react';
import { useToast } from "@/hooks/use-toast";
import { getClients, getStrategies } from '@/api/apiService';
import { Client, Strategy } from '@/api/types';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { placeOrder } from '@/api/apiService';

const PlaceOrder: React.FC = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [clientId, setClientId] = useState<string>('');
  const [strategyId, setStrategyId] = useState<string>('');
  const [symbol, setSymbol] = useState<string>('');
  const [variety, setVariety] = useState<string>('NORMAL');
  const [expiryDate, setExpiryDate] = useState<string>('');
  const [expiryType, setExpiryType] = useState<string>('CE');
  const [strike, setStrike] = useState<string>('');
  const [price, setPrice] = useState<string>('');
  const [qty, setQty] = useState<number>(1);
  const [type, setType] = useState<'BUY' | 'SELL'>('BUY');
  const { toast } = useToast();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [clientsResponse, strategiesResponse] = await Promise.all([
        getClients(),
        getStrategies()
      ]);

      if (clientsResponse.status === 'success') {
        setClients(clientsResponse.data.data);
      } else {
        toast({
          title: "Error",
          description: "Failed to load clients",
          variant: "destructive"
        });
      }

      if (strategiesResponse.status === 'success') {
        setStrategies(strategiesResponse.data.data);
      } else {
        toast({
          title: "Error",
          description: "Failed to load strategies",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!clientId || !strategyId || !symbol || !expiryDate || !expiryType || !strike || !price || !qty || !type) {
      toast({
        title: "Error",
        description: "Please fill in all fields.",
        variant: "destructive"
      });
      return;
    }

    const selectedClient = clients.find(client => client.id === clientId);
    const selectedStrategy = strategies.find(strategy => strategy.id === strategyId);

    if (!selectedClient || !selectedStrategy) {
      toast({
        title: "Error",
        description: "Selected client or strategy not found.",
        variant: "destructive"
      });
      return;
    }

    const orderData = {
      client_id: clientId,
      strategy_id: strategyId,
      symbol,
      variety,
      expiry_date: expiryDate,
      expiry_type: expiryType,
      strike,
      price,
      qty,
      type,
      client_name: selectedClient.name,
      strategy_name: selectedStrategy.name,
    };

    try {
      const response = await placeOrder(orderData);
      if (response.status === 'success') {
        toast({
          title: "Success",
          description: "Order placed successfully!",
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to place order",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Place Order</h1>
      <Card className="p-6">
        <form onSubmit={handleSubmit} className="grid gap-4">
          <div>
            <Label htmlFor="client">Client</Label>
            <Select onValueChange={setClientId}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a client" />
              </SelectTrigger>
              <SelectContent>
                {clients.map((client) => (
                  <SelectItem key={client.id} value={client.id}>
                    {client.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="strategy">Strategy</Label>
            <Select onValueChange={setStrategyId}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a strategy" />
              </SelectTrigger>
              <SelectContent>
                {strategies.map((strategy) => (
                  <SelectItem key={strategy.id} value={strategy.id}>
                    {strategy.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="symbol">Symbol</Label>
            <Input
              type="text"
              id="symbol"
              value={symbol}
              onChange={(e) => setSymbol(e.target.value)}
              placeholder="e.g., RELIANCE"
            />
          </div>
          <div>
            <Label htmlFor="variety">Variety</Label>
            <Select onValueChange={(value) => setVariety(value)}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select variety" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="NORMAL">NORMAL</SelectItem>
                <SelectItem value="ROBO">ROBO</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="expiryDate">Expiry Date</Label>
            <Input
              type="date"
              id="expiryDate"
              value={expiryDate}
              onChange={(e) => setExpiryDate(e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="expiryType">Expiry Type</Label>
            <Select onValueChange={setExpiryType}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select expiry type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="CE">CE</SelectItem>
                <SelectItem value="PE">PE</SelectItem>
                <SelectItem value="FUT">FUT</SelectItem>
                <SelectItem value="EQ">EQ</SelectItem>
                <SelectItem value="OPT">OPT</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="strike">Strike</Label>
            <Input
              type="text"
              id="strike"
              value={strike}
              onChange={(e) => setStrike(e.target.value)}
              placeholder="e.g., 2500"
            />
          </div>
          <div>
            <Label htmlFor="price">Price</Label>
            <Input
              type="text"
              id="price"
              value={price}
              onChange={(e) => setPrice(e.target.value)}
              placeholder="e.g., 2450.50"
            />
          </div>
          <div>
            <Label htmlFor="qty">Quantity</Label>
            <Input
              type="number"
              id="qty"
              value={qty}
              onChange={(e) => setQty(parseInt(e.target.value))}
              placeholder="e.g., 1"
            />
          </div>
          <div>
            <Label htmlFor="type">Type</Label>
            <Select onValueChange={value => setType(value === 'BUY' ? 'BUY' : 'SELL')}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select order type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="BUY">BUY</SelectItem>
                <SelectItem value="SELL">SELL</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button type="submit">Place Order</Button>
        </form>
      </Card>
    </div>
  );
};

export default PlaceOrder;
