
import React, { useState, useEffect } from 'react';
import { useToast } from "@/hooks/use-toast";
import { getAuditTrail } from '@/api/apiService';
import { AuditTrail as AuditTrailType } from '@/api/types';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Filter, RefreshCw } from 'lucide-react';
import DataTableFilter from '@/components/Filters/DataTableFilter';
import { ApiConfig } from '@/api/config';

const AuditTrailPage: React.FC = () => {
  const [auditTrails, setAuditTrails] = useState<AuditTrailType[]>([]);
  const [filteredAuditTrails, setFilteredAuditTrails] = useState<AuditTrailType[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('api');
  const { toast } = useToast();

  useEffect(() => {
    fetchAuditTrail();
  }, [ApiConfig.useMockData]); // Re-fetch when API toggle changes

  const fetchAuditTrail = async () => {
    try {
      setLoading(true);
      const response = await getAuditTrail();
      if (response.status === 'success') {
        // Map API response to component structure if needed
        const formattedAuditTrails = response.data.data.map((trail: any) => ({
          id: trail.id,
          timestamp: trail.timestamp,
          clientId: trail.client_id || trail.clientId,
          clientName: trail.client_name || trail.clientName,
          strategyId: trail.strategy_id || trail.strategyId,
          strategyName: trail.strategy_name || trail.strategyName,
          strike: trail.strike,
          expiry_type: trail.expiry_type,
          expirydate: trail.expirydate,
          request: trail.request,
          response: trail.response,
          status: trail.status,
          error: trail.error || trail.error_message
        }));
        
        setAuditTrails(formattedAuditTrails);
        setFilteredAuditTrails(formattedAuditTrails);
      } else {
        toast({
          title: "Error",
          description: "Failed to load audit trail data",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFilter = (filters: Record<string, string>) => {
    if (Object.values(filters).every(value => value === '')) {
      setFilteredAuditTrails(auditTrails);
      return;
    }

    const filtered = auditTrails.filter(trail => {
      return Object.entries(filters).every(([key, value]) => {
        if (!value) return true;
        
        if (key === 'search') {
          const searchValue = value.toLowerCase();
          return (
            trail.clientName.toLowerCase().includes(searchValue) ||
            trail.strategyName.toLowerCase().includes(searchValue) ||
            trail.status.toLowerCase().includes(searchValue)
          );
        }
        
        // Special case for clientId and strategyId
        if (key === 'clientId' && trail.clientName.toLowerCase().includes(value.toLowerCase())) {
          return true;
        }
        
        if (key === 'strategyId' && trail.strategyName.toLowerCase().includes(value.toLowerCase())) {
          return true;
        }
        
        const trailValue = (trail as any)[key];
        if (!trailValue) return false;
        
        return trailValue.toString().toLowerCase().includes(value.toLowerCase());
      });
    });
    
    setFilteredAuditTrails(filtered);
  };

  const handleReset = () => {
    setFilteredAuditTrails(auditTrails);
  };

  const filterFields = [
    { key: 'clientName', label: 'Client Name', type: 'text' as const },
    { key: 'strategyName', label: 'Strategy Name', type: 'text' as const },
    { key: 'timestamp', label: 'Timestamp', type: 'date' as const },
    { key: 'status', label: 'Status', type: 'select' as const, options: [
      { value: 'SUCCESS', label: 'SUCCESS' },
      { value: 'FAILED', label: 'FAILED' },
    ]}
  ];

  const formatRequestResponse = (data: string) => {
    try {
      // Check if data is already a JSON string
      const parsed = JSON.parse(data);
      return JSON.stringify(parsed, null, 2).substring(0, 100) + '...';
    } catch (e) {
      // If not valid JSON, just truncate
      return data.substring(0, 100) + (data.length > 100 ? '...' : '');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-pulse text-xl">Loading audit trail...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Audit Trail</h1>
        <Button variant="outline" onClick={fetchAuditTrail}>
          <RefreshCw size={16} className="mr-2" /> Refresh
        </Button>
      </div>

      <Card className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="order">Angel One Order Audit Trail</TabsTrigger>
            <TabsTrigger value="api">API Signal Audit Trail</TabsTrigger>
          </TabsList>
          
          <TabsContent value="api">
            <div className="space-y-6">
              <DataTableFilter 
                fields={filterFields}
                onFilter={handleFilter}
                onReset={handleReset}
              />

              <div className="table-container">
                <table className="data-table">
                  <thead>
                    <tr>
                      <th>TIMESTAMP</th>
                      <th>AUDIT ID</th>
                      <th>CLIENT NAME</th>
                      <th>STRATEGY NAME</th>
                      <th>STRIKE</th>
                      <th>EXPIRY TYPE</th>
                      <th>EXPIRY DATE</th>
                      <th>REQUEST</th>
                      <th>RESPONSE</th>
                      <th>STATUS</th>
                      <th>ERROR</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredAuditTrails.map((trail) => (
                      <tr key={trail.id}>
                        <td>{trail.timestamp}</td>
                        <td className="font-mono text-xs">{trail.id.substring(0, 8)}...</td>
                        <td>{trail.clientName}</td>
                        <td>{trail.strategyName}</td>
                        <td>{trail.strike || '-'}</td>
                        <td>{trail.expiry_type || '-'}</td>
                        <td>{trail.expirydate || '-'}</td>
                        <td className="font-mono text-xs max-w-[200px] truncate">
                          <div className="tooltip" title={trail.request}>
                            {formatRequestResponse(trail.request)}
                          </div>
                        </td>
                        <td className="font-mono text-xs max-w-[200px] truncate">
                          <div className="tooltip" title={trail.response}>
                            {formatRequestResponse(trail.response)}
                          </div>
                        </td>
                        <td>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            trail.status === 'SUCCESS' ? 'bg-accent text-accent-foreground' : 'bg-destructive text-destructive-foreground'
                          }`}>
                            {trail.status}
                          </span>
                        </td>
                        <td className="text-destructive">{trail.error || '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                
                {filteredAuditTrails.length === 0 && (
                  <div className="text-center p-6">
                    <p className="text-muted-foreground">No audit trail records found</p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="order">
            <div className="text-center p-12">
              <h3 className="text-lg font-medium mb-2">Angel One Order Audit Trail</h3>
              <p className="text-muted-foreground">This feature is coming soon</p>
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
};

export default AuditTrailPage;
