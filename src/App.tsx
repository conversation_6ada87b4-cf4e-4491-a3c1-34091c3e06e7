
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";

// Layouts
import MainLayout from "./components/Layout/MainLayout";

// Pages
import Index from "./pages/Index";
import Clients from "./pages/Clients";
import Strategies from "./pages/Strategies";
import ClientStrategies from "./pages/ClientStrategies";
import PlaceOrder from "./pages/PlaceOrder";
import Orders from "./pages/Orders";
import Statistics from "./pages/Statistics";
import BrokerAudit from "./pages/BrokerAudit";
import SignalAudit from "./pages/SignalAudit";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/clients" element={<MainLayout><Clients /></MainLayout>} />
          <Route path="/strategies" element={<MainLayout><Strategies /></MainLayout>} />
          <Route path="/client-strategies" element={<MainLayout><ClientStrategies /></MainLayout>} />
          <Route path="/place-order" element={<MainLayout><PlaceOrder /></MainLayout>} />
          <Route path="/orders" element={<MainLayout><Orders /></MainLayout>} />
          <Route path="/statistics" element={<MainLayout><Statistics /></MainLayout>} />
          <Route path="/broker-audit" element={<MainLayout><BrokerAudit /></MainLayout>} />
          <Route path="/signal-audit" element={<MainLayout><SignalAudit /></MainLayout>} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
