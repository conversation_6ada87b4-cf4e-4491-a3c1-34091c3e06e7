// Utility functions for order type chip colors based on expiry type

export type OrderType = 'BUY' | 'SELL';
export type ExpiryType = 'CE' | 'PE' | 'FUT' | 'FUTURE' | 'EQ';

export interface OrderTypeChipConfig {
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  className: string;
}

/**
 * Get chip color configuration for order type based on expiry type
 * 
 * Rules:
 * - CE + BUY = Green
 * - PE + BUY = Red  
 * - CE + SELL = Red
 * - PE + SELL = Green
 * - FUT/FUTURE = Blue
 * - EQ = Yellow
 */
export const getOrderTypeChipConfig = (
  orderType: OrderType, 
  expiryType: ExpiryType
): OrderTypeChipConfig => {
  // Normalize expiry type
  const normalizedExpiryType = expiryType?.toUpperCase();
  const normalizedOrderType = orderType?.toUpperCase();

  // Handle FUT/FUTURE - always blue
  if (normalizedExpiryType === 'FUT' || normalizedExpiryType === 'FUTURE') {
    return {
      backgroundColor: 'bg-blue-100',
      textColor: 'text-blue-800',
      borderColor: 'border-blue-200',
      className: 'bg-blue-100 text-blue-800 border-blue-200'
    };
  }

  // Handle EQ - always yellow
  if (normalizedExpiryType === 'EQ') {
    return {
      backgroundColor: 'bg-yellow-100',
      textColor: 'text-yellow-800',
      borderColor: 'border-yellow-200',
      className: 'bg-yellow-100 text-yellow-800 border-yellow-200'
    };
  }

  // Handle CE and PE based on order type
  if (normalizedExpiryType === 'CE') {
    if (normalizedOrderType === 'BUY') {
      // CE + BUY = Green
      return {
        backgroundColor: 'bg-green-100',
        textColor: 'text-green-800',
        borderColor: 'border-green-200',
        className: 'bg-green-100 text-green-800 border-green-200'
      };
    } else {
      // CE + SELL = Red
      return {
        backgroundColor: 'bg-red-100',
        textColor: 'text-red-800',
        borderColor: 'border-red-200',
        className: 'bg-red-100 text-red-800 border-red-200'
      };
    }
  }

  if (normalizedExpiryType === 'PE') {
    if (normalizedOrderType === 'BUY') {
      // PE + BUY = Red
      return {
        backgroundColor: 'bg-red-100',
        textColor: 'text-red-800',
        borderColor: 'border-red-200',
        className: 'bg-red-100 text-red-800 border-red-200'
      };
    } else {
      // PE + SELL = Green
      return {
        backgroundColor: 'bg-green-100',
        textColor: 'text-green-800',
        borderColor: 'border-green-200',
        className: 'bg-green-100 text-green-800 border-green-200'
      };
    }
  }

  // Default fallback - neutral gray
  return {
    backgroundColor: 'bg-gray-100',
    textColor: 'text-gray-800',
    borderColor: 'border-gray-200',
    className: 'bg-gray-100 text-gray-800 border-gray-200'
  };
};

/**
 * Get chip color configuration for expiry type
 */
export const getExpiryTypeChipConfig = (expiryType: ExpiryType): OrderTypeChipConfig => {
  const normalizedExpiryType = expiryType?.toUpperCase();

  switch (normalizedExpiryType) {
    case 'CE':
      return {
        backgroundColor: 'bg-green-100',
        textColor: 'text-green-800',
        borderColor: 'border-green-200',
        className: 'bg-green-100 text-green-800 border-green-200'
      };
    case 'PE':
      return {
        backgroundColor: 'bg-red-100',
        textColor: 'text-red-800',
        borderColor: 'border-red-200',
        className: 'bg-red-100 text-red-800 border-red-200'
      };
    case 'FUT':
    case 'FUTURE':
      return {
        backgroundColor: 'bg-blue-100',
        textColor: 'text-blue-800',
        borderColor: 'border-blue-200',
        className: 'bg-blue-100 text-blue-800 border-blue-200'
      };
    case 'EQ':
      return {
        backgroundColor: 'bg-yellow-100',
        textColor: 'text-yellow-800',
        borderColor: 'border-yellow-200',
        className: 'bg-yellow-100 text-yellow-800 border-yellow-200'
      };
    default:
      return {
        backgroundColor: 'bg-gray-100',
        textColor: 'text-gray-800',
        borderColor: 'border-gray-200',
        className: 'bg-gray-100 text-gray-800 border-gray-200'
      };
  }
};

/**
 * Get simple BUY/SELL chip configuration
 * BUY = Green, SELL = Red
 */
export const getBuySellChipConfig = (buySell: string): OrderTypeChipConfig => {
  const normalizedBuySell = buySell?.toUpperCase();

  switch (normalizedBuySell) {
    case 'BUY':
      return {
        backgroundColor: 'bg-green-100',
        textColor: 'text-green-800',
        borderColor: 'border-green-200',
        className: 'bg-green-100 text-green-800 border-green-200'
      };
    case 'SELL':
      return {
        backgroundColor: 'bg-red-100',
        textColor: 'text-red-800',
        borderColor: 'border-red-200',
        className: 'bg-red-100 text-red-800 border-red-200'
      };
    default:
      return {
        backgroundColor: 'bg-gray-100',
        textColor: 'text-gray-800',
        borderColor: 'border-gray-200',
        className: 'bg-gray-100 text-gray-800 border-gray-200'
      };
  }
};

/**
 * Get simple status chip configuration
 */
export const getStatusChipConfig = (status: string): OrderTypeChipConfig => {
  const normalizedStatus = status?.toUpperCase();

  switch (normalizedStatus) {
    case 'COMPLETE':
    case 'SUCCESS':
    case 'FILLED':
      return {
        backgroundColor: 'bg-green-100',
        textColor: 'text-green-800',
        borderColor: 'border-green-200',
        className: 'bg-green-100 text-green-800 border-green-200'
      };
    case 'PENDING':
    case 'OPEN':
    case 'PARTIAL':
      return {
        backgroundColor: 'bg-yellow-100',
        textColor: 'text-yellow-800',
        borderColor: 'border-yellow-200',
        className: 'bg-yellow-100 text-yellow-800 border-yellow-200'
      };
    case 'FAILED':
    case 'REJECTED':
    case 'CANCELLED':
      return {
        backgroundColor: 'bg-red-100',
        textColor: 'text-red-800',
        borderColor: 'border-red-200',
        className: 'bg-red-100 text-red-800 border-red-200'
      };
    default:
      return {
        backgroundColor: 'bg-gray-100',
        textColor: 'text-gray-800',
        borderColor: 'border-gray-200',
        className: 'bg-gray-100 text-gray-800 border-gray-200'
      };
  }
};
